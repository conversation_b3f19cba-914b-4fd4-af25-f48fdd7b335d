use duckdb::core::{LogicalTypeH<PERSON>le, LogicalTypeId};
use indexmap::IndexMap;
use std::collections::HashMap;
use crate::projection::{PathToken, ProjectionTree};

/// Represents the schema of a JSON value with DuckDB type mapping
#[derive(Debug, Clone)]
pub enum JsonSchema {
    Primitive(PrimitiveType),
    Struct {
        fields: IndexMap<String, JsonSchema>,
        field_order: Vec<String>,
    },
    List {
        element_schema: Box<JsonSchema>,
        max_capacity: usize,
    },
}

/// Primitive JSON types that map to DuckDB types
#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum PrimitiveType {
    <PERSON>olean,
    Integer,
    Float,
    String,
    Null,
}

/// JSON root document types
#[derive(Debug, Clone)]
pub enum JsonRootType {
    SingleObject,
    ArrayOfObjects,
    ArrayOfPrimitives,
    SinglePrimitive,
    MixedArray,
}

impl JsonSchema {
    /// Create a new primitive schema
    pub fn primitive(ptype: PrimitiveType) -> Self {
        JsonSchema::Primitive(ptype)
    }

    /// Create a new struct schema
    pub fn struct_schema(fields: IndexMap<String, JsonSchema>) -> Self {
        let field_order = fields.keys().cloned().collect();
        JsonSchema::Struct { fields, field_order }
    }

    /// Create a new list schema
    pub fn list_schema(element_schema: JsonSchema, max_capacity: usize) -> Self {
        JsonSchema::List {
            element_schema: Box::new(element_schema),
            max_capacity,
        }
    }

    /// Convert JsonSchema to DuckDB LogicalTypeHandle
    pub fn to_duckdb_type(&self) -> LogicalTypeHandle {
        match self {
            JsonSchema::Primitive(ptype) => ptype.to_duckdb_type(),
            JsonSchema::Struct { fields, .. } => {
                // Create struct type with child types
                let child_pairs: Vec<(&str, LogicalTypeHandle)> = fields
                    .iter()
                    .map(|(name, schema)| (name.as_str(), schema.to_duckdb_type()))
                    .collect();

                LogicalTypeHandle::struct_type(&child_pairs)
            }
            JsonSchema::List { element_schema, .. } => {
                let element_type = element_schema.to_duckdb_type();
                LogicalTypeHandle::list(&element_type)
            }
        }
    }

    /// Merge two schemas, handling type conflicts
    pub fn merge(&self, other: &JsonSchema) -> JsonSchema {
        match (self, other) {
            (JsonSchema::Primitive(a), JsonSchema::Primitive(b)) => {
                JsonSchema::Primitive(a.merge(b))
            }
            (JsonSchema::Struct { fields: a_fields, .. }, JsonSchema::Struct { fields: b_fields, .. }) => {
                let mut merged_fields = a_fields.clone();
                
                // Add fields from b that don't exist in a
                for (name, schema) in b_fields {
                    if let Some(existing_schema) = merged_fields.get(name) {
                        merged_fields.insert(name.clone(), existing_schema.merge(schema));
                    } else {
                        merged_fields.insert(name.clone(), schema.clone());
                    }
                }
                
                JsonSchema::struct_schema(merged_fields)
            }
            (JsonSchema::List { element_schema: a_elem, max_capacity: a_cap }, 
             JsonSchema::List { element_schema: b_elem, max_capacity: b_cap }) => {
                let merged_element = a_elem.merge(b_elem);
                let merged_capacity = (*a_cap).max(*b_cap);
                JsonSchema::list_schema(merged_element, merged_capacity)
            }
            // Type conflicts: promote to most general type
            (JsonSchema::Primitive(_), JsonSchema::Struct { .. }) |
            (JsonSchema::Struct { .. }, JsonSchema::Primitive(_)) => {
                // Convert to string representation for mixed types
                JsonSchema::Primitive(PrimitiveType::String)
            }
            (JsonSchema::Primitive(_), JsonSchema::List { .. }) |
            (JsonSchema::List { .. }, JsonSchema::Primitive(_)) => {
                // Convert to string representation for mixed types
                JsonSchema::Primitive(PrimitiveType::String)
            }
            (JsonSchema::Struct { .. }, JsonSchema::List { .. }) |
            (JsonSchema::List { .. }, JsonSchema::Struct { .. }) => {
                // Convert to string representation for mixed types
                JsonSchema::Primitive(PrimitiveType::String)
            }
        }
    }

    /// Calculate estimated memory requirements for this schema
    pub fn estimate_memory_size(&self, row_count: usize) -> usize {
        match self {
            JsonSchema::Primitive(ptype) => ptype.memory_size() * row_count,
            JsonSchema::Struct { fields, .. } => {
                fields.values().map(|schema| schema.estimate_memory_size(row_count)).sum()
            }
            JsonSchema::List { element_schema, max_capacity } => {
                let element_size = element_schema.estimate_memory_size(*max_capacity);
                element_size * row_count
            }
        }
    }

    /// Check if this schema supports projection of the given field path
    pub fn supports_projection(&self, path: &[String]) -> bool {
        if path.is_empty() {
            return true;
        }

        match self {
            JsonSchema::Struct { fields, .. } => {
                if let Some(field_schema) = fields.get(&path[0]) {
                    field_schema.supports_projection(&path[1..])
                } else {
                    false
                }
            }
            JsonSchema::List { element_schema, .. } => {
                // For lists, we can project into the element schema
                element_schema.supports_projection(path)
            }
            JsonSchema::Primitive(_) => path.is_empty(),
        }
    }

    /// Create a projection-filtered schema that only includes projected fields
    pub fn create_projected_schema(&self, projection_tree: &ProjectionTree) -> JsonSchema {
        self.create_projected_schema_recursive(projection_tree.root(), &[])
    }

    /// Recursively create projected schema based on projection tree
    fn create_projected_schema_recursive(&self, proj_node: &crate::projection::ProjNode, current_path: &[PathToken]) -> JsonSchema {
        match (self, proj_node) {
            (JsonSchema::Primitive(ptype), crate::projection::ProjNode::Leaf { .. }) => {
                JsonSchema::Primitive(ptype.clone())
            }
            (JsonSchema::Struct { fields, field_order }, crate::projection::ProjNode::Object { children, .. }) => {
                let mut projected_fields = IndexMap::new();
                let mut projected_order = Vec::new();

                for field_name in field_order {
                    if let Some(child_node) = children.get(field_name) {
                        if let Some(field_schema) = fields.get(field_name) {
                            let mut new_path = current_path.to_vec();
                            new_path.push(PathToken::Key(field_name.clone()));

                            let projected_field_schema = field_schema.create_projected_schema_recursive(child_node, &new_path);
                            projected_fields.insert(field_name.clone(), projected_field_schema);
                            projected_order.push(field_name.clone());
                        }
                    }
                }

                JsonSchema::Struct {
                    fields: projected_fields,
                    field_order: projected_order,
                }
            }
            (JsonSchema::List { element_schema, max_capacity }, crate::projection::ProjNode::List { elem, .. }) => {
                let mut new_path = current_path.to_vec();
                new_path.push(PathToken::ArrayElem);

                let projected_element_schema = element_schema.create_projected_schema_recursive(elem, &new_path);
                JsonSchema::List {
                    element_schema: Box::new(projected_element_schema),
                    max_capacity: *max_capacity,
                }
            }
            _ => {
                // Fallback for mismatched schema/projection types
                self.clone()
            }
        }
    }

    /// Validate that a projection tree is compatible with this schema
    pub fn validate_projection(&self, projection_tree: &ProjectionTree) -> Result<(), String> {
        self.validate_projection_recursive(projection_tree.root(), &[])
    }

    /// Recursively validate projection tree against schema
    fn validate_projection_recursive(&self, proj_node: &crate::projection::ProjNode, current_path: &[PathToken]) -> Result<(), String> {
        match (self, proj_node) {
            (JsonSchema::Primitive(_), crate::projection::ProjNode::Leaf { .. }) => Ok(()),
            (JsonSchema::Struct { fields, .. }, crate::projection::ProjNode::Object { children, .. }) => {
                for (field_name, child_node) in children {
                    if let Some(field_schema) = fields.get(field_name) {
                        let mut new_path = current_path.to_vec();
                        new_path.push(PathToken::Key(field_name.clone()));
                        field_schema.validate_projection_recursive(child_node, &new_path)?;
                    } else {
                        return Err(format!("Field '{}' not found in schema at path {:?}", field_name, current_path));
                    }
                }
                Ok(())
            }
            (JsonSchema::List { element_schema, .. }, crate::projection::ProjNode::List { elem, .. }) => {
                let mut new_path = current_path.to_vec();
                new_path.push(PathToken::ArrayElem);
                element_schema.validate_projection_recursive(elem, &new_path)
            }
            _ => {
                Err(format!("Schema type mismatch with projection at path {:?}", current_path))
            }
        }
    }

    /// Get the schema for a specific field path
    pub fn get_schema_at_path(&self, path: &[PathToken]) -> Option<&JsonSchema> {
        if path.is_empty() {
            return Some(self);
        }

        match (self, &path[0]) {
            (JsonSchema::Struct { fields, .. }, PathToken::Key(field_name)) => {
                if let Some(field_schema) = fields.get(field_name) {
                    field_schema.get_schema_at_path(&path[1..])
                } else {
                    None
                }
            }
            (JsonSchema::List { element_schema, .. }, PathToken::ArrayElem) => {
                element_schema.get_schema_at_path(&path[1..])
            }
            _ => None,
        }
    }
}

impl PrimitiveType {
    /// Convert PrimitiveType to DuckDB LogicalTypeHandle
    pub fn to_duckdb_type(&self) -> LogicalTypeHandle {
        match self {
            PrimitiveType::Boolean => LogicalTypeHandle::from(LogicalTypeId::Boolean),
            PrimitiveType::Integer => LogicalTypeHandle::from(LogicalTypeId::Bigint),
            PrimitiveType::Float => LogicalTypeHandle::from(LogicalTypeId::Double),
            PrimitiveType::String => LogicalTypeHandle::from(LogicalTypeId::Varchar),
            PrimitiveType::Null => LogicalTypeHandle::from(LogicalTypeId::Varchar), // Nullable string
        }
    }

    /// Merge two primitive types, promoting to more general type if needed
    pub fn merge(&self, other: &PrimitiveType) -> PrimitiveType {
        match (self, other) {
            (PrimitiveType::Null, other) | (other, PrimitiveType::Null) => other.clone(),
            (PrimitiveType::Boolean, PrimitiveType::Boolean) => PrimitiveType::Boolean,
            (PrimitiveType::Integer, PrimitiveType::Integer) => PrimitiveType::Integer,
            (PrimitiveType::Float, PrimitiveType::Float) => PrimitiveType::Float,
            (PrimitiveType::String, PrimitiveType::String) => PrimitiveType::String,
            // Integer can be promoted to Float
            (PrimitiveType::Integer, PrimitiveType::Float) |
            (PrimitiveType::Float, PrimitiveType::Integer) => PrimitiveType::Float,
            // Everything else promotes to String
            _ => PrimitiveType::String,
        }
    }

    /// Get estimated memory size for this primitive type
    pub fn memory_size(&self) -> usize {
        match self {
            PrimitiveType::Boolean => 1,
            PrimitiveType::Integer => 8,
            PrimitiveType::Float => 8,
            PrimitiveType::String => 32, // Average string size estimate
            PrimitiveType::Null => 1,
        }
    }
}

/// Schema inference configuration
#[derive(Debug, Clone)]
pub struct SchemaInferenceConfig {
    pub sample_size: usize,
    pub max_depth: i32,
    pub field_appearance_threshold: f64,
    pub map_inference_threshold: i64,
}

impl Default for SchemaInferenceConfig {
    fn default() -> Self {
        Self {
            sample_size: 20480,
            max_depth: -1, // No limit
            field_appearance_threshold: 0.1,
            map_inference_threshold: 200,
        }
    }
}

/// Schema inference statistics
#[derive(Debug, Default)]
pub struct SchemaStats {
    pub objects_sampled: usize,
    pub max_nesting_depth: usize,
    pub field_frequencies: HashMap<String, usize>,
    pub type_conflicts: usize,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_primitive_type_merge() {
        assert_eq!(
            PrimitiveType::Integer.merge(&PrimitiveType::Float),
            PrimitiveType::Float
        );
        assert_eq!(
            PrimitiveType::Boolean.merge(&PrimitiveType::String),
            PrimitiveType::String
        );
        assert_eq!(
            PrimitiveType::Null.merge(&PrimitiveType::Integer),
            PrimitiveType::Integer
        );
    }

    #[test]
    fn test_schema_memory_estimation() {
        let schema = JsonSchema::primitive(PrimitiveType::Integer);
        assert_eq!(schema.estimate_memory_size(100), 800);

        let mut fields = IndexMap::new();
        fields.insert("id".to_string(), JsonSchema::primitive(PrimitiveType::Integer));
        fields.insert("name".to_string(), JsonSchema::primitive(PrimitiveType::String));
        let struct_schema = JsonSchema::struct_schema(fields);
        assert_eq!(struct_schema.estimate_memory_size(100), 4000); // 8 + 32 = 40 per row
    }

    #[test]
    fn test_projection_support() {
        let mut fields = IndexMap::new();
        fields.insert("user".to_string(), {
            let mut user_fields = IndexMap::new();
            user_fields.insert("id".to_string(), JsonSchema::primitive(PrimitiveType::Integer));
            user_fields.insert("name".to_string(), JsonSchema::primitive(PrimitiveType::String));
            JsonSchema::struct_schema(user_fields)
        });
        let schema = JsonSchema::struct_schema(fields);

        assert!(schema.supports_projection(&["user".to_string(), "id".to_string()]));
        assert!(!schema.supports_projection(&["nonexistent".to_string()]));
    }

    #[test]
    fn test_get_schema_at_path() {
        let mut fields = IndexMap::new();
        fields.insert("name".to_string(), JsonSchema::primitive(PrimitiveType::String));

        let mut nested_fields = IndexMap::new();
        nested_fields.insert("street".to_string(), JsonSchema::primitive(PrimitiveType::String));
        nested_fields.insert("city".to_string(), JsonSchema::primitive(PrimitiveType::String));
        let address_schema = JsonSchema::struct_schema(nested_fields);

        fields.insert("address".to_string(), address_schema);

        let schema = JsonSchema::struct_schema(fields);

        // Test path lookup
        let path = vec![PathToken::Key("address".to_string()), PathToken::Key("street".to_string())];
        let found_schema = schema.get_schema_at_path(&path);
        assert!(found_schema.is_some());

        if let Some(JsonSchema::Primitive(ptype)) = found_schema {
            assert_eq!(*ptype, PrimitiveType::String);
        } else {
            panic!("Expected primitive string schema");
        }
    }

    #[test]
    fn test_projected_schema_creation() {
        let mut fields = IndexMap::new();
        fields.insert("name".to_string(), JsonSchema::primitive(PrimitiveType::String));
        fields.insert("age".to_string(), JsonSchema::primitive(PrimitiveType::Integer));
        fields.insert("email".to_string(), JsonSchema::primitive(PrimitiveType::String));

        let schema = JsonSchema::struct_schema(fields);

        // Create projection tree for only "name" and "age" (columns 0 and 1)
        let projected_columns = vec![0u64, 1u64];
        let projection_tree = ProjectionTree::new(&schema, &projected_columns).unwrap();

        // Create projected schema
        let projected_schema = schema.create_projected_schema(&projection_tree);

        if let JsonSchema::Struct { fields: proj_fields, field_order: proj_order } = projected_schema {
            assert_eq!(proj_fields.len(), 2);
            assert_eq!(proj_order.len(), 2);
            assert!(proj_fields.contains_key("name"));
            assert!(proj_fields.contains_key("age"));
            assert!(!proj_fields.contains_key("email"));
        } else {
            panic!("Expected struct schema");
        }
    }
}
