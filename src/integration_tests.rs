use crate::projection::{ProjectionTree, PathToken};
use crate::schema::{JsonSchema, PrimitiveType};
use crate::null_handling::{<PERSON><PERSON><PERSON><PERSON><PERSON>, NullHandlingStrategy};
use crate::error_handling::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorHandlingMode};
use crate::projection_integration::ProjectionAwareProcessor;
use crate::vector_populator::JsonValue;
use indexmap::IndexMap;
use std::io::Cursor;

/// Test data structures for comprehensive testing
mod test_data {
    use super::*;

    /// Simple flat JSON object
    pub fn simple_object() -> &'static str {
        r#"{"name": "<PERSON>", "age": 30, "email": "<EMAIL>"}"#
    }

    /// Nested JSON object
    pub fn nested_object() -> &'static str {
        r#"{
            "user": {
                "name": "Alice",
                "profile": {
                    "age": 25,
                    "location": "NYC"
                }
            },
            "metadata": {
                "created": "2023-01-01",
                "version": 1
            }
        }"#
    }

    /// JSON with arrays
    pub fn array_object() -> &'static str {
        r#"{
            "users": [
                {"name": "<PERSON>", "age": 30},
                {"name": "Jane", "age": 25}
            ],
            "tags": ["important", "urgent"],
            "scores": [95, 87, 92]
        }"#
    }

    /// Complex nested structure
    pub fn complex_nested() -> &'static str {
        r#"{
            "company": {
                "name": "TechCorp",
                "departments": [
                    {
                        "name": "Engineering",
                        "employees": [
                            {
                                "name": "Alice",
                                "skills": ["rust", "python"],
                                "projects": [
                                    {"name": "ProjectA", "status": "active"},
                                    {"name": "ProjectB", "status": "completed"}
                                ]
                            }
                        ]
                    }
                ]
            }
        }"#
    }

    /// Create a simple schema for testing
    pub fn create_simple_schema() -> JsonSchema {
        let mut fields = IndexMap::new();
        fields.insert("name".to_string(), JsonSchema::primitive(PrimitiveType::String));
        fields.insert("age".to_string(), JsonSchema::primitive(PrimitiveType::Integer));
        fields.insert("email".to_string(), JsonSchema::primitive(PrimitiveType::String));
        JsonSchema::struct_schema(fields)
    }

    /// Create a nested schema for testing
    pub fn create_nested_schema() -> JsonSchema {
        let mut profile_fields = IndexMap::new();
        profile_fields.insert("age".to_string(), JsonSchema::primitive(PrimitiveType::Integer));
        profile_fields.insert("location".to_string(), JsonSchema::primitive(PrimitiveType::String));

        let mut user_fields = IndexMap::new();
        user_fields.insert("name".to_string(), JsonSchema::primitive(PrimitiveType::String));
        user_fields.insert("profile".to_string(), JsonSchema::struct_schema(profile_fields));

        let mut metadata_fields = IndexMap::new();
        metadata_fields.insert("created".to_string(), JsonSchema::primitive(PrimitiveType::String));
        metadata_fields.insert("version".to_string(), JsonSchema::primitive(PrimitiveType::Integer));

        let mut root_fields = IndexMap::new();
        root_fields.insert("user".to_string(), JsonSchema::struct_schema(user_fields));
        root_fields.insert("metadata".to_string(), JsonSchema::struct_schema(metadata_fields));

        JsonSchema::struct_schema(root_fields)
    }
}

/// Test projection tree functionality
mod projection_tests {
    use super::*;

    #[test]
    fn test_simple_projection_tree() {
        let schema = test_data::create_simple_schema();
        let projected_columns = vec![0u64, 2u64]; // Project "name" and "email", skip "age"
        
        let projection_tree = ProjectionTree::new(&schema, &projected_columns);
        assert!(projection_tree.is_ok());
        
        let tree = projection_tree.unwrap();
        assert_eq!(tree.total_columns(), 2);
        
        // Test path parsing
        let name_path = vec![PathToken::Key("name".to_string())];
        assert!(tree.should_parse_path(&name_path));
        
        let age_path = vec![PathToken::Key("age".to_string())];
        assert!(!tree.should_parse_path(&age_path)); // Should be skipped
        
        let email_path = vec![PathToken::Key("email".to_string())];
        assert!(tree.should_parse_path(&email_path));
    }

    #[test]
    fn test_nested_projection_tree() {
        let schema = test_data::create_nested_schema();
        let projected_columns = vec![0u64, 1u64]; // Project both user and metadata
        
        let projection_tree = ProjectionTree::new(&schema, &projected_columns);
        assert!(projection_tree.is_ok());
        
        let tree = projection_tree.unwrap();
        
        // Test nested path parsing
        let user_name_path = vec![
            PathToken::Key("user".to_string()),
            PathToken::Key("name".to_string())
        ];
        assert!(tree.should_parse_path(&user_name_path));
        
        let metadata_version_path = vec![
            PathToken::Key("metadata".to_string()),
            PathToken::Key("version".to_string())
        ];
        assert!(tree.should_parse_path(&metadata_version_path));
    }

    #[test]
    fn test_projection_effectiveness() {
        let schema = test_data::create_simple_schema();
        
        // Test high selectivity (only 1 out of 3 columns)
        let high_selectivity = vec![0u64];
        let tree = ProjectionTree::new(&schema, &high_selectivity).unwrap();
        assert_eq!(tree.total_columns(), 1);
        
        // Test low selectivity (all columns)
        let low_selectivity = vec![0u64, 1u64, 2u64];
        let tree = ProjectionTree::new(&schema, &low_selectivity).unwrap();
        assert_eq!(tree.total_columns(), 3);
    }
}

/// Test null handling functionality
mod null_handling_tests {
    use super::*;

    #[test]
    fn test_null_handler_strict_mode() {
        let mut handler = NullHandler::new(NullHandlingStrategy::Strict);
        
        handler.start_row();
        
        // Mark some fields as seen
        let name_path = vec![PathToken::Key("name".to_string())];
        handler.mark_field_seen(&name_path);
        
        assert!(handler.was_field_seen(&name_path));
        
        let age_path = vec![PathToken::Key("age".to_string())];
        assert!(!handler.was_field_seen(&age_path));
    }

    #[test]
    fn test_null_handler_lenient_mode() {
        let handler = NullHandler::new(NullHandlingStrategy::Lenient);
        assert_eq!(handler.strategy(), &NullHandlingStrategy::Lenient);
        
        // In lenient mode, missing fields should not be required
        let path = vec![PathToken::Key("optional_field".to_string())];
        assert!(!handler.is_field_required(&path));
    }

    #[test]
    fn test_default_values() {
        let handler = NullHandler::new(NullHandlingStrategy::FillDefaults);
        
        // Test default values for different primitive types
        assert_eq!(
            handler.get_default_for_primitive(&PrimitiveType::String),
            JsonValue::String(String::new())
        );
        assert_eq!(
            handler.get_default_for_primitive(&PrimitiveType::Integer),
            JsonValue::Integer(0)
        );
        assert_eq!(
            handler.get_default_for_primitive(&PrimitiveType::Boolean),
            JsonValue::Boolean(false)
        );
    }
}

/// Test error handling functionality
mod error_handling_tests {
    use super::*;
    use crate::error_handling::utils;

    #[test]
    fn test_error_collector_modes() {
        // Test strict mode
        let mut strict_collector = ErrorCollector::new(ErrorHandlingMode::Strict);
        let error = utils::type_mismatch_error(
            vec![PathToken::Key("test".to_string())],
            "string",
            "number"
        );
        assert!(strict_collector.add_error(error).is_err());

        // Test lenient mode
        let mut lenient_collector = ErrorCollector::new(ErrorHandlingMode::Lenient);
        let error = utils::type_mismatch_error(
            vec![PathToken::Key("test".to_string())],
            "string",
            "number"
        );
        assert!(lenient_collector.add_error(error).is_ok());
        assert_eq!(lenient_collector.warnings().len(), 1);

        // Test collect mode
        let mut collect_collector = ErrorCollector::new(ErrorHandlingMode::Collect);
        let error1 = utils::missing_field_error(
            "required_field",
            vec![PathToken::Key("test".to_string())],
            "validation"
        );
        let error2 = utils::schema_validation_error(
            vec![PathToken::Key("another".to_string())],
            "string",
            "number",
            "Type mismatch"
        );
        
        collect_collector.add_error(error1).unwrap();
        collect_collector.add_error(error2).unwrap();
        
        assert_eq!(collect_collector.errors().len(), 2);
        assert!(collect_collector.has_errors());
        
        let summary = collect_collector.error_summary();
        assert_eq!(summary.total_errors, 2);
        assert!(summary.error_types.contains_key("Missing Field"));
        assert!(summary.error_types.contains_key("Schema Validation"));
    }

    #[test]
    fn test_error_categorization() {
        let mut collector = ErrorCollector::new(ErrorHandlingMode::Collect);
        
        // Add different types of errors
        let type_error = utils::type_mismatch_error(
            vec![PathToken::Key("field1".to_string())],
            "string",
            "number"
        );
        let missing_error = utils::missing_field_error(
            "field2",
            vec![PathToken::Key("parent".to_string())],
            "validation"
        );
        
        collector.add_error(type_error).unwrap();
        collector.add_error(missing_error).unwrap();
        
        let summary = collector.error_summary();
        assert_eq!(summary.error_types.get("Type Mismatch"), Some(&1));
        assert_eq!(summary.error_types.get("Missing Field"), Some(&1));
    }
}

/// Memory usage tests
mod memory_tests {
    use super::*;

    #[test]
    fn test_projection_memory_efficiency() {
        // This test would ideally measure actual memory usage
        // For now, we test the logical efficiency
        
        let schema = test_data::create_simple_schema();
        
        // Full projection (all columns)
        let full_projection = vec![0u64, 1u64, 2u64];
        let full_tree = ProjectionTree::new(&schema, &full_projection).unwrap();
        
        // Partial projection (subset of columns)
        let partial_projection = vec![0u64]; // Only first column
        let partial_tree = ProjectionTree::new(&schema, &partial_projection).unwrap();
        
        // Partial projection should have fewer columns
        assert!(partial_tree.total_columns() < full_tree.total_columns());
        assert_eq!(partial_tree.total_columns(), 1);
        assert_eq!(full_tree.total_columns(), 3);
    }

    #[test]
    fn test_nested_projection_efficiency() {
        let schema = test_data::create_nested_schema();
        
        // Project only user.name (deep path)
        let selective_projection = vec![0u64]; // Assuming this maps to user.name
        let tree = ProjectionTree::new(&schema, &selective_projection).unwrap();
        
        // Should be able to skip metadata entirely
        let metadata_path = vec![PathToken::Key("metadata".to_string())];
        // Note: This test depends on the actual implementation of projection tree
        // The exact behavior may vary based on how the projection tree is built
        
        assert!(tree.total_columns() > 0); // Should have at least one column
    }
}

/// Integration tests combining multiple components
mod integration_tests {
    use super::*;

    #[test]
    fn test_end_to_end_simple_projection() {
        let schema = test_data::create_simple_schema();
        let projected_columns = vec![0u64, 2u64]; // name and email only
        let json_data = test_data::simple_object();
        
        let reader = Cursor::new(json_data.as_bytes());
        
        // This test would create a full processor and test the integration
        // For now, we test the components individually
        let projection_tree = ProjectionTree::new(&schema, &projected_columns);
        assert!(projection_tree.is_ok());
        
        let tree = projection_tree.unwrap();
        
        // Verify projection works as expected
        let name_path = vec![PathToken::Key("name".to_string())];
        let age_path = vec![PathToken::Key("age".to_string())];
        let email_path = vec![PathToken::Key("email".to_string())];
        
        assert!(tree.should_parse_path(&name_path));
        assert!(!tree.should_parse_path(&age_path)); // Should be skipped
        assert!(tree.should_parse_path(&email_path));
    }

    #[test]
    fn test_error_handling_integration() {
        let mut error_collector = ErrorCollector::new(ErrorHandlingMode::Collect);
        let null_handler = NullHandler::new(NullHandlingStrategy::Lenient);
        
        // Simulate processing with errors
        let error = crate::error_handling::utils::type_mismatch_error(
            vec![PathToken::Key("problematic_field".to_string())],
            "string",
            "number"
        );
        
        error_collector.add_error(error).unwrap();
        
        // Verify error was collected
        assert!(error_collector.has_errors());
        assert_eq!(error_collector.errors().len(), 1);
        
        // Verify null handler works independently
        assert_eq!(null_handler.strategy(), &NullHandlingStrategy::Lenient);
    }

    #[test]
    fn test_complex_nested_structure() {
        let schema = test_data::create_nested_schema();
        let json_data = test_data::nested_object();
        
        // Test that we can create projection trees for complex structures
        let all_columns = vec![0u64, 1u64];
        let projection_tree = ProjectionTree::new(&schema, &all_columns);
        assert!(projection_tree.is_ok());
        
        let tree = projection_tree.unwrap();
        
        // Test deep path navigation
        let deep_path = vec![
            PathToken::Key("user".to_string()),
            PathToken::Key("profile".to_string()),
            PathToken::Key("age".to_string())
        ];
        
        // This should be parseable if the projection includes the user subtree
        assert!(tree.should_parse_path(&deep_path));
    }
}

/// Performance and benchmarking tests
mod performance_tests {
    use super::*;

    #[test]
    fn test_projection_tree_creation_performance() {
        let schema = test_data::create_nested_schema();
        
        // Measure time to create projection tree (basic performance test)
        let start = std::time::Instant::now();
        
        for _ in 0..100 {
            let projected_columns = vec![0u64, 1u64];
            let _tree = ProjectionTree::new(&schema, &projected_columns).unwrap();
        }
        
        let duration = start.elapsed();
        
        // This is a basic performance test - in a real scenario,
        // you'd want more sophisticated benchmarking
        assert!(duration.as_millis() < 1000); // Should be fast
    }

    #[test]
    fn test_path_checking_performance() {
        let schema = test_data::create_nested_schema();
        let projected_columns = vec![0u64, 1u64];
        let tree = ProjectionTree::new(&schema, &projected_columns).unwrap();
        
        let test_path = vec![
            PathToken::Key("user".to_string()),
            PathToken::Key("name".to_string())
        ];
        
        let start = std::time::Instant::now();
        
        for _ in 0..1000 {
            let _should_parse = tree.should_parse_path(&test_path);
        }
        
        let duration = start.elapsed();
        
        // Path checking should be very fast
        assert!(duration.as_millis() < 100);
    }
}
