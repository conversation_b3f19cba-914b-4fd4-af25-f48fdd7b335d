extern crate duckdb;
extern crate duckdb_loadable_macros;
extern crate libduckdb_sys;

mod schema;
mod stream_processor;
mod vector_populator;
mod projection;
mod stack_parser;
mod nested_vectors;
mod projection_integration;
mod null_handling;
mod error_handling;

#[cfg(test)]
mod integration_tests;

use duckdb::{
    core::{DataChunkHandle, LogicalTypeHandle, LogicalTypeId},
    vtab::{BindInfo, InitInfo, TableFunctionInfo, VTab},
    Connection, Result,
};
use duckdb_loadable_macros::duckdb_entrypoint_c_api;
use libduckdb_sys as ffi;
use schema::{JsonSchema, JsonRootType, SchemaInferenceConfig};
use stream_processor::JsonStreamProcessor;
use vector_populator::JsonValue;
use struson::reader::JsonReader;
use std::error::Error;
use std::sync::atomic::{AtomicUsize, Ordering};

#[repr(C)]
struct JsonStreamBindData {
    file_path: String,
    schema: JsonSchema,
    root_type: JsonRootType,
}

#[repr(C)]
struct JsonStreamInitData {
    current_row: AtomicUsize,
    total_rows: AtomicUsize,
    projected_columns: Vec<u64>, // Column indices that are projected (idx_t from DuckDB)
}

struct JsonStreamVTab;

impl VTab for JsonStreamVTab {
    type InitData = JsonStreamInitData;
    type BindData = JsonStreamBindData;

    fn bind(bind: &BindInfo) -> Result<Self::BindData, Box<dyn std::error::Error>> {
        // Get the file path parameter
        let file_path = bind.get_parameter(0).to_string();

        // Perform schema inference
        let mut processor = JsonStreamProcessor::from_file(&file_path)?;
        let config = SchemaInferenceConfig::default();
        let (schema, root_type, _stats) = processor.infer_schema(&config)?;

        // Add result columns based on inferred schema
        match &root_type {
            JsonRootType::SingleObject | JsonRootType::ArrayOfObjects => {
                if let JsonSchema::Struct { fields, field_order } = &schema {
                    for field_name in field_order {
                        if let Some(field_schema) = fields.get(field_name) {
                            let duckdb_type = field_schema.to_duckdb_type();
                            bind.add_result_column(field_name, duckdb_type);
                        }
                    }
                }
            }
            _ => {
                // For primitive arrays or single primitives, create a single column
                let duckdb_type = schema.to_duckdb_type();
                bind.add_result_column("value", duckdb_type);
            }
        }

        Ok(JsonStreamBindData {
            file_path,
            schema,
            root_type,
        })
    }

    fn init(init_info: &InitInfo) -> Result<Self::InitData, Box<dyn std::error::Error>> {
        // Capture projected column indices for optimization
        let projected_columns = init_info.get_column_indices();

        Ok(JsonStreamInitData {
            current_row: AtomicUsize::new(0),
            total_rows: AtomicUsize::new(0), // Will be determined during execution
            projected_columns,
        })
    }

    fn func(func: &TableFunctionInfo<Self>, output: &mut DataChunkHandle) -> Result<(), Box<dyn std::error::Error>> {
        let init_data = func.get_init_data();
        let bind_data = func.get_bind_data();

        // Create a new JSON processor for this chunk
        let mut processor = JsonStreamProcessor::from_file(&bind_data.file_path)?;

        // Process JSON data based on root type
        match &bind_data.root_type {
            JsonRootType::ArrayOfObjects => {
                Self::process_array_of_objects(&mut processor, &bind_data.schema, init_data, output)?;
            }
            JsonRootType::SingleObject => {
                Self::process_single_object(&mut processor, &bind_data.schema, init_data, output)?;
            }
            JsonRootType::ArrayOfPrimitives => {
                Self::process_array_of_primitives(&mut processor, &bind_data.schema, init_data, output)?;
            }
            JsonRootType::SinglePrimitive => {
                Self::process_single_primitive(&mut processor, &bind_data.schema, init_data, output)?;
            }
            JsonRootType::MixedArray => {
                Self::process_mixed_array(&mut processor, &bind_data.schema, init_data, output)?;
            }
        }

        Ok(())
    }

    fn parameters() -> Option<Vec<LogicalTypeHandle>> {
        Some(vec![LogicalTypeHandle::from(LogicalTypeId::Varchar)])
    }

    fn supports_pushdown() -> bool {
        true // Enable projection pushdown
    }
}

impl JsonStreamVTab {
    /// Check if a column index is projected (should be processed)
    fn is_column_projected(column_idx: usize, projected_columns: &[u64]) -> bool {
        projected_columns.contains(&(column_idx as u64))
    }

    /// Get the output vector index for a schema field index (handles projection mapping)
    fn get_output_vector_index(schema_field_idx: usize, projected_columns: &[u64]) -> Option<usize> {
        projected_columns.iter().position(|&col| col == schema_field_idx as u64)
    }

    /// Process array of objects JSON structure
    fn process_array_of_objects(
        processor: &mut JsonStreamProcessor<std::io::BufReader<std::fs::File>>,
        schema: &JsonSchema,
        init_data: &JsonStreamInitData,
        output: &mut DataChunkHandle,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Skip to the current position in the array
        processor.reader.begin_array()?;

        // Skip already processed rows
        let current_row = init_data.current_row.load(Ordering::Relaxed);
        for _i in 0..current_row {
            if processor.reader.has_next()? {
                processor.reader.skip_value()?;
            } else {
                output.set_len(0);
                return Ok(());
            }
        }

        let mut rows_processed = 0;
        const BATCH_SIZE: usize = 1000;

        // Process up to BATCH_SIZE rows
        while processor.reader.has_next()? && rows_processed < BATCH_SIZE {
            if let JsonSchema::Struct { fields, field_order } = schema {
                // Read the JSON object (with projection optimization)
                processor.reader.begin_object()?;
                let mut field_values = std::collections::HashMap::new();

                while processor.reader.has_next()? {
                    let field_name = processor.reader.next_name()?.to_string();

                    // Check if this field is projected before reading its value
                    let field_idx = field_order.iter().position(|name| name == &field_name);
                    let should_read = field_idx.map_or(false, |idx| {
                        Self::is_column_projected(idx, &init_data.projected_columns)
                    });

                    if should_read {
                        let value = Self::read_json_value(&mut processor.reader)?;
                        field_values.insert(field_name, value);
                    } else {
                        // Skip this field to save parsing time
                        processor.reader.skip_value()?;
                    }
                }
                processor.reader.end_object()?;

                // Populate vectors for this row (only projected columns)
                for (field_idx, field_name) in field_order.iter().enumerate() {
                    // Only process projected columns
                    if Self::is_column_projected(field_idx, &init_data.projected_columns) {
                        if let Some(field_schema) = fields.get(field_name) {
                            // Map schema field index to output vector index
                            if let Some(output_vector_idx) = Self::get_output_vector_index(field_idx, &init_data.projected_columns) {
                                let field_value = field_values.get(field_name);
                                Self::populate_vector_field(output, output_vector_idx, field_schema, field_value, rows_processed)?;
                            }
                        }
                    }
                }

                rows_processed += 1;
            } else {
                return Err("Schema mismatch: expected struct schema for array of objects".into());
            }
        }

        // Update current row count
        init_data.current_row.fetch_add(rows_processed, Ordering::Relaxed);

        // Check if we've reached the end of the data
        let has_more_data = processor.reader.has_next()?;

        if rows_processed == 0 || !has_more_data {
            // No more data to process, signal completion
            output.set_len(0);
        } else {
            // Set the number of rows we processed
            output.set_len(rows_processed);
        }

        Ok(())
    }

    /// Process single object JSON structure
    fn process_single_object(
        _processor: &mut JsonStreamProcessor<std::io::BufReader<std::fs::File>>,
        _schema: &JsonSchema,
        _init_data: &JsonStreamInitData,
        output: &mut DataChunkHandle,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // TODO: Implement single object processing
        output.set_len(0);
        Err("Single object processing not yet implemented".into())
    }

    /// Process array of primitives JSON structure
    fn process_array_of_primitives(
        _processor: &mut JsonStreamProcessor<std::io::BufReader<std::fs::File>>,
        _schema: &JsonSchema,
        _init_data: &JsonStreamInitData,
        output: &mut DataChunkHandle,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // TODO: Implement array of primitives processing
        output.set_len(0);
        Err("Array of primitives processing not yet implemented".into())
    }

    /// Process single primitive JSON structure
    fn process_single_primitive(
        _processor: &mut JsonStreamProcessor<std::io::BufReader<std::fs::File>>,
        _schema: &JsonSchema,
        _init_data: &JsonStreamInitData,
        output: &mut DataChunkHandle,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // TODO: Implement single primitive processing
        output.set_len(0);
        Err("Single primitive processing not yet implemented".into())
    }

    /// Process mixed array JSON structure
    fn process_mixed_array(
        _processor: &mut JsonStreamProcessor<std::io::BufReader<std::fs::File>>,
        _schema: &JsonSchema,
        _init_data: &JsonStreamInitData,
        output: &mut DataChunkHandle,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // TODO: Implement mixed array processing
        output.set_len(0);
        Err("Mixed arrays not yet implemented".into())
    }

    /// Read a JSON value from the stream
    fn read_json_value(
        reader: &mut struson::reader::JsonStreamReader<std::io::BufReader<std::fs::File>>,
    ) -> Result<JsonValue, Box<dyn std::error::Error>> {
        use struson::reader::ValueType;

        match reader.peek()? {
            ValueType::Null => {
                reader.next_null()?;
                Ok(JsonValue::Null)
            }
            ValueType::Boolean => {
                let value = reader.next_bool()?;
                Ok(JsonValue::Boolean(value))
            }
            ValueType::Number => {
                let value = reader.next_number_as_str()?;
                // Try to parse as integer first, then float
                if let Ok(int_val) = value.parse::<i64>() {
                    Ok(JsonValue::Integer(int_val))
                } else if let Ok(float_val) = value.parse::<f64>() {
                    Ok(JsonValue::Float(float_val))
                } else {
                    Err(format!("Invalid number format: {}", value).into())
                }
            }
            ValueType::String => {
                let value = reader.next_str()?;
                Ok(JsonValue::String(value.to_string()))
            }
            ValueType::Array => {
                reader.begin_array()?;
                let mut array = Vec::new();
                while reader.has_next()? {
                    array.push(Self::read_json_value(reader)?);
                }
                reader.end_array()?;
                Ok(JsonValue::Array(array))
            }
            ValueType::Object => {
                reader.begin_object()?;
                let mut object = std::collections::HashMap::new();
                while reader.has_next()? {
                    let key = reader.next_name()?.to_string();
                    let value = Self::read_json_value(reader)?;
                    object.insert(key, value);
                }
                reader.end_object()?;
                Ok(JsonValue::Object(object))
            }
        }
    }

    /// Populate a vector field with a JSON value
    fn populate_vector_field(
        output: &mut DataChunkHandle,
        field_idx: usize,
        field_schema: &JsonSchema,
        field_value: Option<&JsonValue>,
        row_idx: usize,
    ) -> Result<(), Box<dyn std::error::Error>> {
        use duckdb::core::Inserter;

        match field_schema {
            JsonSchema::Primitive(ptype) => {
                match ptype {
                    schema::PrimitiveType::Integer => {
                        let mut vector = output.flat_vector(field_idx);
                        let slice = vector.as_mut_slice::<i64>();
                        if let Some(JsonValue::Integer(val)) = field_value {
                            slice[row_idx] = *val;
                        } else {
                            slice[row_idx] = 0; // Default value for missing/null
                        }
                    }
                    schema::PrimitiveType::Float => {
                        let mut vector = output.flat_vector(field_idx);
                        let slice = vector.as_mut_slice::<f64>();
                        if let Some(JsonValue::Float(val)) = field_value {
                            slice[row_idx] = *val;
                        } else {
                            slice[row_idx] = 0.0; // Default value for missing/null
                        }
                    }
                    schema::PrimitiveType::String => {
                        let vector = output.flat_vector(field_idx);
                        if let Some(JsonValue::String(val)) = field_value {
                            vector.insert(row_idx, val.as_str());
                        } else {
                            vector.insert(row_idx, ""); // Default value for missing/null
                        }
                    }
                    schema::PrimitiveType::Boolean => {
                        let mut vector = output.flat_vector(field_idx);
                        let slice = vector.as_mut_slice::<bool>();
                        if let Some(JsonValue::Boolean(val)) = field_value {
                            slice[row_idx] = *val;
                        } else {
                            slice[row_idx] = false; // Default value for missing/null
                        }
                    }
                    schema::PrimitiveType::Null => {
                        // Null values don't need special handling - just leave the vector uninitialized
                        // DuckDB will handle null values appropriately
                    }
                }
            }
            JsonSchema::Struct { fields, field_order } => {
                let mut struct_vector = output.struct_vector(field_idx);

                if let Some(JsonValue::Object(obj)) = field_value {
                    // Populate each field of the struct
                    for (child_idx, field_name) in field_order.iter().enumerate() {
                        if let Some(child_schema) = fields.get(field_name) {
                            let child_value = obj.get(field_name);
                            Self::populate_struct_child_field(&mut struct_vector, child_idx, child_schema, child_value, row_idx)?;
                        }
                    }
                } else {
                    // Handle null/missing struct - set all child fields to null/default
                    for (child_idx, field_name) in field_order.iter().enumerate() {
                        if let Some(child_schema) = fields.get(field_name) {
                            Self::populate_struct_child_field(&mut struct_vector, child_idx, child_schema, None, row_idx)?;
                        }
                    }
                }
            }
            JsonSchema::List { element_schema, .. } => {
                let mut list_vector = output.list_vector(field_idx);

                if let Some(JsonValue::Array(arr)) = field_value {
                    // Get current child vector length to use as offset
                    let current_child_len = list_vector.len();
                    let array_length = arr.len();

                    // Set the list entry for this row
                    list_vector.set_entry(row_idx, current_child_len, array_length);

                    // Populate the child vector with array elements
                    Self::populate_list_elements(&list_vector, element_schema, arr, current_child_len)?;

                    // Update the list vector length to include the new elements
                    list_vector.set_len(current_child_len + array_length);
                } else {
                    // Handle null/missing array - set empty list
                    let current_child_len = list_vector.len();
                    list_vector.set_entry(row_idx, current_child_len, 0);
                    // No need to update length for empty lists
                }
            }
        }

        Ok(())
    }

    /// Populate a child field within a struct vector
    fn populate_struct_child_field(
        struct_vector: &duckdb::core::StructVector,
        child_idx: usize,
        child_schema: &JsonSchema,
        child_value: Option<&JsonValue>,
        row_idx: usize,
    ) -> Result<(), Box<dyn std::error::Error>> {
        use duckdb::core::Inserter;

        match child_schema {
            JsonSchema::Primitive(ptype) => {
                match ptype {
                    schema::PrimitiveType::Integer => {
                        let mut child_vector = struct_vector.child(child_idx, 1000); // Use reasonable capacity
                        let slice = child_vector.as_mut_slice::<i64>();
                        if let Some(JsonValue::Integer(val)) = child_value {
                            slice[row_idx] = *val;
                        } else {
                            slice[row_idx] = 0; // Default for null/missing
                        }
                    }
                    schema::PrimitiveType::Float => {
                        let mut child_vector = struct_vector.child(child_idx, 1000);
                        let slice = child_vector.as_mut_slice::<f64>();
                        if let Some(JsonValue::Float(val)) = child_value {
                            slice[row_idx] = *val;
                        } else {
                            slice[row_idx] = 0.0; // Default for null/missing
                        }
                    }
                    schema::PrimitiveType::String => {
                        let child_vector = struct_vector.child(child_idx, 1000);
                        if let Some(JsonValue::String(val)) = child_value {
                            child_vector.insert(row_idx, val.as_str());
                        } else {
                            child_vector.insert(row_idx, ""); // Default for null/missing
                        }
                    }
                    schema::PrimitiveType::Boolean => {
                        let mut child_vector = struct_vector.child(child_idx, 1000);
                        let slice = child_vector.as_mut_slice::<bool>();
                        if let Some(JsonValue::Boolean(val)) = child_value {
                            slice[row_idx] = *val;
                        } else {
                            slice[row_idx] = false; // Default for null/missing
                        }
                    }
                    schema::PrimitiveType::Null => {
                        // Null fields don't need population
                    }
                }
            }
            JsonSchema::Struct { fields, field_order } => {
                let child_struct_vector = struct_vector.struct_vector_child(child_idx);

                if let Some(JsonValue::Object(obj)) = child_value {
                    // Recursively populate nested struct
                    for (grandchild_idx, field_name) in field_order.iter().enumerate() {
                        if let Some(grandchild_schema) = fields.get(field_name) {
                            let grandchild_value = obj.get(field_name);
                            Self::populate_struct_child_field(&child_struct_vector, grandchild_idx, grandchild_schema, grandchild_value, row_idx)?;
                        }
                    }
                } else {
                    // Handle null/missing nested struct
                    for (grandchild_idx, field_name) in field_order.iter().enumerate() {
                        if let Some(grandchild_schema) = fields.get(field_name) {
                            Self::populate_struct_child_field(&child_struct_vector, grandchild_idx, grandchild_schema, None, row_idx)?;
                        }
                    }
                }
            }
            JsonSchema::List { element_schema, .. } => {
                let mut list_vector = struct_vector.list_vector_child(child_idx);

                if let Some(JsonValue::Array(arr)) = child_value {
                    // Get current child vector length to use as offset
                    let current_child_len = list_vector.len();
                    let array_length = arr.len();

                    // Set the list entry for this row
                    list_vector.set_entry(row_idx, current_child_len, array_length);

                    // Populate the child vector with array elements
                    Self::populate_list_elements(&list_vector, element_schema, arr, current_child_len)?;

                    // Update the list vector length to include the new elements
                    list_vector.set_len(current_child_len + array_length);
                } else {
                    // Handle null/missing array - set empty list
                    let current_child_len = list_vector.len();
                    list_vector.set_entry(row_idx, current_child_len, 0);
                    // No need to update length for empty lists
                }
            }
        }

        Ok(())
    }

    /// Populate list elements in a list vector
    fn populate_list_elements(
        list_vector: &duckdb::core::ListVector,
        element_schema: &JsonSchema,
        elements: &[JsonValue],
        start_offset: usize,
    ) -> Result<(), Box<dyn std::error::Error>> {
        use duckdb::core::Inserter;

        // Calculate the new total size needed for the child vector
        let new_total_size = start_offset + elements.len();

        match element_schema {
            JsonSchema::Primitive(ptype) => {
                match ptype {
                    schema::PrimitiveType::Integer => {
                        let mut child_vector = list_vector.child(new_total_size);
                        let slice = child_vector.as_mut_slice::<i64>();
                        for (i, element) in elements.iter().enumerate() {
                            let child_idx = start_offset + i;
                            if let JsonValue::Integer(val) = element {
                                slice[child_idx] = *val;
                            } else {
                                slice[child_idx] = 0; // Default for non-integer values
                            }
                        }
                    }
                    schema::PrimitiveType::Float => {
                        let mut child_vector = list_vector.child(new_total_size);
                        let slice = child_vector.as_mut_slice::<f64>();
                        for (i, element) in elements.iter().enumerate() {
                            let child_idx = start_offset + i;
                            if let JsonValue::Float(val) = element {
                                slice[child_idx] = *val;
                            } else {
                                slice[child_idx] = 0.0; // Default for non-float values
                            }
                        }
                    }
                    schema::PrimitiveType::String => {
                        let child_vector = list_vector.child(new_total_size);
                        for (i, element) in elements.iter().enumerate() {
                            let child_idx = start_offset + i;
                            if let JsonValue::String(val) = element {
                                child_vector.insert(child_idx, val.as_str());
                            } else {
                                child_vector.insert(child_idx, ""); // Default for non-string values
                            }
                        }
                    }
                    schema::PrimitiveType::Boolean => {
                        let mut child_vector = list_vector.child(new_total_size);
                        let slice = child_vector.as_mut_slice::<bool>();
                        for (i, element) in elements.iter().enumerate() {
                            let child_idx = start_offset + i;
                            if let JsonValue::Boolean(val) = element {
                                slice[child_idx] = *val;
                            } else {
                                slice[child_idx] = false; // Default for non-boolean values
                            }
                        }
                    }
                    schema::PrimitiveType::Null => {
                        // Ensure child vector is properly sized even for null elements
                        let _child_vector = list_vector.child(new_total_size);
                    }
                }
            }
            JsonSchema::Struct { fields, field_order } => {
                let child_struct_vector = list_vector.struct_child(new_total_size);

                for (i, element) in elements.iter().enumerate() {
                    let child_idx = start_offset + i;
                    if let JsonValue::Object(obj) = element {
                        // Populate each field of the struct element
                        for (field_idx, field_name) in field_order.iter().enumerate() {
                            if let Some(field_schema) = fields.get(field_name) {
                                let field_value = obj.get(field_name);
                                Self::populate_struct_child_field(&child_struct_vector, field_idx, field_schema, field_value, child_idx)?;
                            }
                        }
                    } else {
                        // Handle non-object elements in struct array - populate with defaults
                        for (field_idx, field_name) in field_order.iter().enumerate() {
                            if let Some(field_schema) = fields.get(field_name) {
                                Self::populate_struct_child_field(&child_struct_vector, field_idx, field_schema, None, child_idx)?;
                            }
                        }
                    }
                }
            }
            JsonSchema::List { .. } => {
                // TODO: Implement nested list population (arrays of arrays)
                return Err("Nested list population not yet implemented".into());
            }
        }

        Ok(())
    }
}

const EXTENSION_NAME: &str = env!("CARGO_PKG_NAME");

#[duckdb_entrypoint_c_api()]
pub fn extension_entrypoint(con: Connection) -> Result<(), Box<dyn Error>> {
    con.register_table_function::<JsonStreamVTab>("json_stream")
        .expect("Failed to register json_stream table function");
    Ok(())
}