use crate::schema::JsonSchema;
use std::collections::HashMap;

/// Represents a node in the projection tree for efficient nested field access
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub enum ProjNode {
    /// Object node: maps field names to child nodes
    Object {
        children: HashMap<String, ProjNode>,
        any_required: bool,
    },
    /// List node: single child representing the element schema
    List {
        elem: Box<ProjNode>,
        required: bool,
    },
    /// Leaf node: represents a projected column
    Leaf {
        col_idx: usize, // Index into output column array
    },
}

/// Path token for navigating nested JSON structures
#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum PathToken {
    Key(String),
    ArrayElem, // Represents array element access (no specific index)
}

/// Column specification for projected fields
#[derive(Debug, Clone)]
pub struct ColumnSpec {
    pub path_tokens: Vec<PathToken>,
    pub col_idx: usize,
}

/// Projection tree builder and manager
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct ProjectionTree {
    root: ProjNode,
    column_specs: Vec<ColumnSpec>,
    total_columns: usize,
}

impl ProjNode {
    /// Create a new object node
    pub fn object(children: HashMap<String, ProjNode>) -> Self {
        let any_required = children.values().any(|child| child.is_required());
        ProjNode::Object { children, any_required }
    }

    /// Create a new list node
    pub fn list(elem: ProjNode) -> Self {
        let required = elem.is_required();
        ProjNode::List {
            elem: Box::new(elem),
            required,
        }
    }

    /// Create a new leaf node
    pub fn leaf(col_idx: usize) -> Self {
        ProjNode::Leaf { col_idx }
    }

    /// Check if this node or any descendant is required
    pub fn is_required(&self) -> bool {
        match self {
            ProjNode::Object { any_required, .. } => *any_required,
            ProjNode::List { required, .. } => *required,
            ProjNode::Leaf { .. } => true,
        }
    }

    /// Get child node for a given field name (for Object nodes)
    pub fn get_child(&self, field_name: &str) -> Option<&ProjNode> {
        match self {
            ProjNode::Object { children, .. } => children.get(field_name),
            _ => None,
        }
    }

    /// Get element node (for List nodes)
    pub fn get_element(&self) -> Option<&ProjNode> {
        match self {
            ProjNode::List { elem, .. } => Some(elem.as_ref()),
            _ => None,
        }
    }

    /// Get column index (for Leaf nodes)
    pub fn get_column_index(&self) -> Option<usize> {
        match self {
            ProjNode::Leaf { col_idx } => Some(*col_idx),
            _ => None,
        }
    }
}

impl ProjectionTree {
    /// Create a new projection tree from schema and projected column indices
    pub fn new(
        schema: &JsonSchema,
        projected_columns: &[u64],
    ) -> Result<Self, Box<dyn std::error::Error>> {
        let mut column_specs = Vec::new();
        let mut root_children = HashMap::new();

        // Build projection tree based on schema and projected columns
        match schema {
            JsonSchema::Struct { fields, field_order } => {
                for (field_idx, field_name) in field_order.iter().enumerate() {
                    if projected_columns.contains(&(field_idx as u64)) {
                        if let Some(field_schema) = fields.get(field_name) {
                            // Find output column index
                            let output_col_idx = projected_columns
                                .iter()
                                .position(|&col| col == field_idx as u64)
                                .ok_or("Column index mapping error")?;

                            let path_tokens = vec![PathToken::Key(field_name.clone())];
                            let col_spec = ColumnSpec {
                                path_tokens,
                                col_idx: output_col_idx,
                            };

                            let child_node = Self::build_node_from_schema(
                                field_schema,
                                output_col_idx,
                                &mut column_specs,
                            )?;

                            column_specs.push(col_spec);
                            root_children.insert(field_name.clone(), child_node);
                        }
                    }
                }
            }
            _ => {
                return Err("Projection tree currently only supports struct root schemas".into());
            }
        }

        let root = ProjNode::object(root_children);
        let total_columns = projected_columns.len();

        Ok(ProjectionTree {
            root,
            column_specs,
            total_columns,
        })
    }

    /// Build a projection node from a schema recursively
    fn build_node_from_schema(
        schema: &JsonSchema,
        col_idx: usize,
        column_specs: &mut Vec<ColumnSpec>,
    ) -> Result<ProjNode, Box<dyn std::error::Error>> {
        match schema {
            JsonSchema::Primitive(_) => Ok(ProjNode::leaf(col_idx)),
            JsonSchema::Struct { fields, field_order } => {
                let mut children = HashMap::new();
                
                // For nested structs, we need to handle all fields
                // In a full implementation, this would need to consider
                // nested projection paths, but for now we include all fields
                for field_name in field_order {
                    if let Some(field_schema) = fields.get(field_name) {
                        let child_node = Self::build_node_from_schema(
                            field_schema,
                            col_idx, // Same column index for nested fields
                            column_specs,
                        )?;
                        children.insert(field_name.clone(), child_node);
                    }
                }
                
                Ok(ProjNode::object(children))
            }
            JsonSchema::List { element_schema, .. } => {
                let elem_node = Self::build_node_from_schema(
                    element_schema,
                    col_idx,
                    column_specs,
                )?;
                Ok(ProjNode::list(elem_node))
            }
        }
    }

    /// Get the root projection node
    pub fn root(&self) -> &ProjNode {
        &self.root
    }

    /// Get column specifications
    pub fn column_specs(&self) -> &[ColumnSpec] {
        &self.column_specs
    }

    /// Get total number of projected columns
    pub fn total_columns(&self) -> usize {
        self.total_columns
    }

    /// Check if a field path should be parsed based on projection
    pub fn should_parse_path(&self, path: &[PathToken]) -> bool {
        let mut current_node = &self.root;
        
        for token in path {
            match token {
                PathToken::Key(field_name) => {
                    if let Some(child) = current_node.get_child(field_name) {
                        current_node = child;
                    } else {
                        return false; // Field not projected
                    }
                }
                PathToken::ArrayElem => {
                    if let Some(elem) = current_node.get_element() {
                        current_node = elem;
                    } else {
                        return false; // Not a list or not projected
                    }
                }
            }
        }
        
        current_node.is_required()
    }

    /// Get the column index for a given path (if it's a leaf)
    pub fn get_column_index_for_path(&self, path: &[PathToken]) -> Option<usize> {
        let mut current_node = &self.root;
        
        for token in path {
            match token {
                PathToken::Key(field_name) => {
                    current_node = current_node.get_child(field_name)?;
                }
                PathToken::ArrayElem => {
                    current_node = current_node.get_element()?;
                }
            }
        }
        
        current_node.get_column_index()
    }
}

/// Parse DuckDB nested column expressions into path tokens
pub fn parse_column_path(column_expr: &str) -> Vec<PathToken> {
    let mut tokens = Vec::new();
    let mut current_field = String::new();
    let mut in_brackets = false;
    
    for ch in column_expr.chars() {
        match ch {
            '.' if !in_brackets => {
                if !current_field.is_empty() {
                    tokens.push(PathToken::Key(current_field.clone()));
                    current_field.clear();
                }
            }
            '[' => {
                if !current_field.is_empty() {
                    tokens.push(PathToken::Key(current_field.clone()));
                    current_field.clear();
                }
                in_brackets = true;
            }
            ']' => {
                if in_brackets {
                    // For now, treat any array access as ArrayElem
                    // In a full implementation, we might want to handle specific indices
                    tokens.push(PathToken::ArrayElem);
                    in_brackets = false;
                }
            }
            _ if !in_brackets => {
                current_field.push(ch);
            }
            _ => {
                // Inside brackets - for now we ignore the content
                // In a full implementation, we might parse specific array indices
            }
        }
    }
    
    if !current_field.is_empty() {
        tokens.push(PathToken::Key(current_field));
    }
    
    tokens
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_column_path() {
        assert_eq!(
            parse_column_path("field1"),
            vec![PathToken::Key("field1".to_string())]
        );
        
        assert_eq!(
            parse_column_path("field1.field2"),
            vec![
                PathToken::Key("field1".to_string()),
                PathToken::Key("field2".to_string())
            ]
        );
        
        assert_eq!(
            parse_column_path("field1[0].field2"),
            vec![
                PathToken::Key("field1".to_string()),
                PathToken::ArrayElem,
                PathToken::Key("field2".to_string())
            ]
        );
    }

    #[test]
    fn test_proj_node_creation() {
        let leaf = ProjNode::leaf(0);
        assert!(leaf.is_required());
        assert_eq!(leaf.get_column_index(), Some(0));

        let mut children = HashMap::new();
        children.insert("field1".to_string(), ProjNode::leaf(1));
        let obj = ProjNode::object(children);
        assert!(obj.is_required());
        assert!(obj.get_child("field1").is_some());
        assert!(obj.get_child("nonexistent").is_none());
    }
}
