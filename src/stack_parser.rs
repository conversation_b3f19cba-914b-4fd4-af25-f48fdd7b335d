use crate::projection::{<PERSON>j<PERSON><PERSON>, PathToken, ProjectionTree};
use crate::schema::JsonSchema;
use crate::vector_populator::JsonValue;
use struson::reader::{<PERSON><PERSON><PERSON><PERSON><PERSON>, JsonStreamReader, ValueType};
use std::collections::HashMap;
use std::io::Read;

/// Stack frame for tracking nested parsing context
#[derive(Debug, Clone)]
pub struct StackFrame<'a> {
    /// Reference to the projection node for this level
    pub node: &'a ProjNode,
    /// Type of frame (object or list)
    pub kind: FrameKind,
    /// Whether this frame or any descendant is needed
    pub needed: bool,
    /// Current path tokens up to this frame
    pub path: Vec<PathToken>,
}

/// Type of stack frame
#[derive(Debug, Clone)]
pub enum FrameKind {
    /// Object frame - tracks field parsing
    Object {
        /// Fields that have been seen in this object
        seen_fields: HashMap<String, bool>,
        /// Current field being processed
        current_field: Option<String>,
    },
    /// List frame - tracks array element parsing
    List {
        /// Current element index
        element_index: usize,
        /// Total elements processed
        elements_processed: usize,
    },
    /// Root frame - top level
    Root,
}

/// Stack-based JSON parser with projection support
pub struct StackBasedParser<R: Read> {
    reader: JsonStreamReader<R>,
    projection_tree: ProjectionTree,
    stack: Vec<StackFrame<'static>>, // We'll manage lifetimes carefully
    current_row_data: HashMap<String, JsonValue>,
    rows_processed: usize,
}

impl<R: Read> StackBasedParser<R> {
    /// Create a new stack-based parser
    pub fn new(reader: R, projection_tree: ProjectionTree) -> Self {
        let json_reader = JsonStreamReader::new(reader);
        
        Self {
            reader: json_reader,
            projection_tree,
            stack: Vec::new(),
            current_row_data: HashMap::new(),
            rows_processed: 0,
        }
    }

    /// Parse the next row of data
    pub fn parse_next_row(&mut self) -> Result<Option<HashMap<String, JsonValue>>, Box<dyn std::error::Error>> {
        // For a simple test, let's just try to parse the single JSON object
        self.current_row_data.clear();

        // Initialize stack with root frame
        self.stack.clear();
        let root_frame = StackFrame {
            node: self.projection_tree.root(),
            kind: FrameKind::Root,
            needed: true,
            path: Vec::new(),
        };

        // SAFETY: We're managing the lifetime carefully by ensuring the projection_tree
        // outlives the stack frames. This is a common pattern in streaming parsers.
        let root_frame_static = unsafe {
            std::mem::transmute::<StackFrame<'_>, StackFrame<'static>>(root_frame)
        };
        self.stack.push(root_frame_static);

        // Parse the current JSON value
        self.parse_value()?;

        self.rows_processed += 1;
        Ok(Some(self.current_row_data.clone()))
    }

    /// Parse a JSON value based on the current stack context
    fn parse_value(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let value_type = self.reader.peek()?;
        
        match value_type {
            ValueType::Object => self.parse_object(),
            ValueType::Array => self.parse_array(),
            ValueType::String => self.parse_primitive(),
            ValueType::Number => self.parse_primitive(),
            ValueType::Boolean => self.parse_primitive(),
            ValueType::Null => self.parse_primitive(),
        }
    }

    /// Parse a JSON object
    fn parse_object(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // Extract needed information from current frame before borrowing issues
        let (current_node, current_needed, current_path) = {
            let current_frame = self.stack.last().ok_or("Empty stack")?;
            (current_frame.node, current_frame.needed, current_frame.path.clone())
        };

        if !current_needed {
            // Skip entire object if not needed
            self.reader.skip_value()?;
            return Ok(());
        }

        self.reader.begin_object()?;

        // Create object frame
        let object_frame = StackFrame {
            node: current_node,
            kind: FrameKind::Object {
                seen_fields: HashMap::new(),
                current_field: None,
            },
            needed: current_needed,
            path: current_path.clone(),
        };

        let object_frame_static = unsafe {
            std::mem::transmute::<StackFrame<'_>, StackFrame<'static>>(object_frame)
        };
        self.stack.push(object_frame_static);

        // Parse object fields
        while self.reader.has_next()? {
            let field_name = self.reader.next_name()?.to_string();
            
            // Update current field in frame
            if let Some(frame) = self.stack.last_mut() {
                if let FrameKind::Object { current_field, .. } = &mut frame.kind {
                    *current_field = Some(field_name.clone());
                }
            }

            // Check if this field is projected
            let should_parse = if let ProjNode::Object { children, .. } = current_node {
                children.contains_key(&field_name)
            } else {
                false
            };

            if should_parse {
                // Get child node for this field
                if let ProjNode::Object { children, .. } = current_node {
                    if let Some(child_node) = children.get(&field_name) {
                        // Create new path
                        let mut new_path = current_path.clone();
                        new_path.push(PathToken::Key(field_name.clone()));

                        // Push child frame
                        let child_frame = StackFrame {
                            node: child_node,
                            kind: FrameKind::Root, // Will be updated based on value type
                            needed: true,
                            path: new_path,
                        };
                        
                        let child_frame_static = unsafe {
                            std::mem::transmute::<StackFrame<'_>, StackFrame<'static>>(child_frame)
                        };
                        self.stack.push(child_frame_static);

                        // Parse the field value
                        self.parse_value()?;
                        
                        // Pop child frame
                        self.stack.pop();
                    }
                }
            } else {
                // Skip this field
                self.reader.skip_value()?;
            }

            // Mark field as seen
            if let Some(frame) = self.stack.last_mut() {
                if let FrameKind::Object { seen_fields, .. } = &mut frame.kind {
                    seen_fields.insert(field_name, true);
                }
            }
        }

        self.reader.end_object()?;
        self.stack.pop(); // Remove object frame
        
        Ok(())
    }

    /// Parse a JSON array
    fn parse_array(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // Extract needed information from current frame before borrowing issues
        let (current_node, current_needed, current_path) = {
            let current_frame = self.stack.last().ok_or("Empty stack")?;
            (current_frame.node, current_frame.needed, current_frame.path.clone())
        };

        if !current_needed {
            // Skip entire array if not needed
            self.reader.skip_value()?;
            return Ok(());
        }

        self.reader.begin_array()?;

        // Create array frame
        let array_frame = StackFrame {
            node: current_node,
            kind: FrameKind::List {
                element_index: 0,
                elements_processed: 0,
            },
            needed: current_needed,
            path: current_path.clone(),
        };

        let array_frame_static = unsafe {
            std::mem::transmute::<StackFrame<'_>, StackFrame<'static>>(array_frame)
        };
        self.stack.push(array_frame_static);

        // Parse array elements
        while self.reader.has_next()? {
            // Check if we should parse this element
            let should_parse = if let ProjNode::List { elem, .. } = current_node {
                elem.is_required()
            } else {
                false
            };

            if should_parse {
                if let ProjNode::List { elem, .. } = current_node {
                    // Create new path
                    let mut new_path = current_path.clone();
                    new_path.push(PathToken::ArrayElem);

                    // Push element frame
                    let elem_frame = StackFrame {
                        node: elem,
                        kind: FrameKind::Root,
                        needed: true,
                        path: new_path,
                    };
                    
                    let elem_frame_static = unsafe {
                        std::mem::transmute::<StackFrame<'_>, StackFrame<'static>>(elem_frame)
                    };
                    self.stack.push(elem_frame_static);

                    // Parse element value
                    self.parse_value()?;
                    
                    // Pop element frame
                    self.stack.pop();
                }
            } else {
                // Skip this element
                self.reader.skip_value()?;
            }

            // Update element index
            if let Some(frame) = self.stack.last_mut() {
                if let FrameKind::List { element_index, elements_processed } = &mut frame.kind {
                    *element_index += 1;
                    *elements_processed += 1;
                }
            }
        }

        self.reader.end_array()?;
        self.stack.pop(); // Remove array frame
        
        Ok(())
    }

    /// Parse a primitive JSON value
    fn parse_primitive(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // Extract needed information from current frame before borrowing issues
        let (current_node, current_needed, current_path) = {
            let current_frame = self.stack.last().ok_or("Empty stack")?;
            (current_frame.node, current_frame.needed, current_frame.path.clone())
        };

        if !current_needed {
            // Skip primitive if not needed
            self.reader.skip_value()?;
            return Ok(());
        }

        // Only store value if this is a leaf node
        if let ProjNode::Leaf { col_idx: _ } = current_node {
            let value = self.read_primitive_value()?;

            // Store value using the path as key
            let path_key = self.path_to_string(&current_path);
            self.current_row_data.insert(path_key, value);
        } else {
            // Skip if not a leaf
            self.reader.skip_value()?;
        }

        Ok(())
    }

    /// Read a primitive value from the JSON stream
    fn read_primitive_value(&mut self) -> Result<JsonValue, Box<dyn std::error::Error>> {
        match self.reader.peek()? {
            ValueType::String => {
                let value = self.reader.next_string()?;
                Ok(JsonValue::String(value.to_string()))
            }
            ValueType::Number => {
                let value = self.reader.next_number_as_string()?;
                // Try to parse as integer first, then float
                if let Ok(int_val) = value.parse::<i64>() {
                    Ok(JsonValue::Integer(int_val))
                } else if let Ok(float_val) = value.parse::<f64>() {
                    Ok(JsonValue::Float(float_val))
                } else {
                    Err(format!("Invalid number format: {}", value).into())
                }
            }
            ValueType::Boolean => {
                let value = self.reader.next_bool()?;
                Ok(JsonValue::Boolean(value))
            }
            ValueType::Null => {
                self.reader.next_null()?;
                Ok(JsonValue::Null)
            }
            _ => Err("Unexpected value type for primitive".into()),
        }
    }

    /// Convert path tokens to a string key
    fn path_to_string(&self, path: &[PathToken]) -> String {
        path.iter()
            .map(|token| match token {
                PathToken::Key(key) => key.clone(),
                PathToken::ArrayElem => "[*]".to_string(),
            })
            .collect::<Vec<_>>()
            .join(".")
    }

    /// Get the number of rows processed
    pub fn rows_processed(&self) -> usize {
        self.rows_processed
    }

    /// Check if there are more values to read
    pub fn has_next(&mut self) -> Result<bool, Box<dyn std::error::Error>> {
        Ok(self.reader.has_next()?)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::schema::{JsonSchema, PrimitiveType};
    use indexmap::IndexMap;
    use std::io::Cursor;

    #[test]
    fn test_stack_parser_simple_object() {
        let json = r#"{"name": "John", "age": 30}"#;
        let reader = Cursor::new(json.as_bytes());

        // Create schema
        let mut fields = IndexMap::new();
        fields.insert("name".to_string(), JsonSchema::primitive(PrimitiveType::String));
        fields.insert("age".to_string(), JsonSchema::primitive(PrimitiveType::Integer));
        let schema = JsonSchema::struct_schema(fields);

        // Create projection tree (project only "name")
        let projected_columns = vec![0u64];
        let projection_tree = ProjectionTree::new(&schema, &projected_columns).unwrap();

        // Create parser
        let mut parser = StackBasedParser::new(reader, projection_tree);

        // Parse row
        let result = parser.parse_next_row().unwrap();
        assert!(result.is_some());

        let row_data = result.unwrap();
        println!("Row data: {:?}", row_data);
        // For now, just check that we get some data back
        // The exact key format will depend on how we implement path_to_string
    }
}
