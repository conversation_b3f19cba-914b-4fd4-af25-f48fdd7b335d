use crate::projection::{ProjN<PERSON>, PathToken, ProjectionTree};
use crate::schema::JsonSchema;
use crate::vector_populator::JsonValue;
use duckdb::core::{DataChunkHandle, FlatVector, ListVector, StructVector, Inserter};
use std::collections::HashMap;

/// Manages accumulation of list data during parsing with memory optimization
#[derive(Debug)]
pub struct ListAccumulator {
    /// Child values for this list
    pub child_values: Vec<JsonValue>,
    /// Offsets for each list entry (start index in child_values)
    pub offsets: Vec<usize>,
    /// Lengths for each list entry
    pub lengths: Vec<usize>,
    /// Validity bitmap for list entries
    pub validity: Vec<bool>,
    /// Current total child count
    pub total_child_count: usize,
    /// Memory management settings
    pub initial_capacity: usize,
    pub growth_factor: f64,
    pub max_chunk_size: usize,
    /// Statistics for memory optimization
    pub max_list_size_seen: usize,
    pub total_lists_processed: usize,
}

impl ListAccumulator {
    /// Create a new list accumulator with default settings
    pub fn new() -> Self {
        Self::with_capacity(1024) // Default initial capacity
    }

    /// Create a new list accumulator with specified initial capacity
    pub fn with_capacity(initial_capacity: usize) -> Self {
        Self {
            child_values: Vec::with_capacity(initial_capacity),
            offsets: Vec::new(),
            lengths: Vec::new(),
            validity: Vec::new(),
            total_child_count: 0,
            initial_capacity,
            growth_factor: 1.5, // 50% growth
            max_chunk_size: 65536, // 64K elements max per chunk
            max_list_size_seen: 0,
            total_lists_processed: 0,
        }
    }

    /// Add a list entry with memory management
    pub fn add_list(&mut self, values: Vec<JsonValue>) -> Result<(), Box<dyn std::error::Error>> {
        let start_offset = self.total_child_count;
        let length = values.len();

        // Update statistics
        self.max_list_size_seen = self.max_list_size_seen.max(length);
        self.total_lists_processed += 1;

        // Check if we need to grow the child_values vector
        let required_capacity = self.total_child_count + length;
        if required_capacity > self.child_values.capacity() {
            self.grow_child_values(required_capacity)?;
        }

        // Check for chunk size limits
        if self.total_child_count + length > self.max_chunk_size {
            return Err(format!(
                "List would exceed max chunk size: {} + {} > {}",
                self.total_child_count, length, self.max_chunk_size
            ).into());
        }

        self.offsets.push(start_offset);
        self.lengths.push(length);
        self.validity.push(true);

        self.child_values.extend(values);
        self.total_child_count += length;

        Ok(())
    }

    /// Grow the child_values vector with exponential growth
    fn grow_child_values(&mut self, required_capacity: usize) -> Result<(), Box<dyn std::error::Error>> {
        let current_capacity = self.child_values.capacity();
        let new_capacity = if current_capacity == 0 {
            self.initial_capacity.max(required_capacity)
        } else {
            let growth_capacity = (current_capacity as f64 * self.growth_factor) as usize;
            growth_capacity.max(required_capacity)
        };

        // Prevent excessive memory allocation
        if new_capacity > self.max_chunk_size * 2 {
            return Err(format!(
                "Requested capacity {} exceeds maximum allowed: {}",
                new_capacity, self.max_chunk_size * 2
            ).into());
        }

        self.child_values.reserve(new_capacity - current_capacity);
        Ok(())
    }

    /// Add a null list entry
    pub fn add_null_list(&mut self) {
        let start_offset = self.total_child_count;
        
        self.offsets.push(start_offset);
        self.lengths.push(0);
        self.validity.push(false);
        // No child values added for null lists
    }

    /// Get the number of list entries
    pub fn len(&self) -> usize {
        self.offsets.len()
    }

    /// Check if empty
    pub fn is_empty(&self) -> bool {
        self.offsets.is_empty()
    }

    /// Get memory usage statistics
    pub fn memory_stats(&self) -> MemoryStats {
        MemoryStats {
            child_values_capacity: self.child_values.capacity(),
            child_values_len: self.child_values.len(),
            offsets_capacity: self.offsets.capacity(),
            offsets_len: self.offsets.len(),
            total_memory_bytes: self.estimate_memory_usage(),
            max_list_size_seen: self.max_list_size_seen,
            total_lists_processed: self.total_lists_processed,
        }
    }

    /// Estimate total memory usage in bytes
    pub fn estimate_memory_usage(&self) -> usize {
        let child_values_size = self.child_values.capacity() * std::mem::size_of::<JsonValue>();
        let offsets_size = self.offsets.capacity() * std::mem::size_of::<usize>();
        let lengths_size = self.lengths.capacity() * std::mem::size_of::<usize>();
        let validity_size = self.validity.capacity() * std::mem::size_of::<bool>();

        child_values_size + offsets_size + lengths_size + validity_size
    }

    /// Optimize memory usage by shrinking vectors to fit
    pub fn shrink_to_fit(&mut self) {
        self.child_values.shrink_to_fit();
        self.offsets.shrink_to_fit();
        self.lengths.shrink_to_fit();
        self.validity.shrink_to_fit();
    }

    /// Reset for reuse with optional capacity preservation
    pub fn reset(&mut self, preserve_capacity: bool) {
        if preserve_capacity {
            self.child_values.clear();
            self.offsets.clear();
            self.lengths.clear();
            self.validity.clear();
        } else {
            self.child_values = Vec::with_capacity(self.initial_capacity);
            self.offsets = Vec::new();
            self.lengths = Vec::new();
            self.validity = Vec::new();
        }

        self.total_child_count = 0;
        // Keep statistics for optimization
    }

    /// Check if we're approaching memory limits
    pub fn is_near_capacity(&self) -> bool {
        let usage_ratio = self.total_child_count as f64 / self.max_chunk_size as f64;
        usage_ratio > 0.8 // 80% threshold
    }

    /// Get recommended chunk size based on observed patterns
    pub fn get_recommended_chunk_size(&self) -> usize {
        if self.total_lists_processed == 0 {
            return self.max_chunk_size;
        }

        let avg_list_size = self.total_child_count / self.total_lists_processed;
        let safety_factor = 2.0; // Allow for variance

        ((avg_list_size as f64 * safety_factor) as usize).min(self.max_chunk_size)
    }
}

/// Memory usage statistics for list accumulators
#[derive(Debug, Clone)]
pub struct MemoryStats {
    pub child_values_capacity: usize,
    pub child_values_len: usize,
    pub offsets_capacity: usize,
    pub offsets_len: usize,
    pub total_memory_bytes: usize,
    pub max_list_size_seen: usize,
    pub total_lists_processed: usize,
}

/// Manages population of nested DuckDB vectors
#[derive(Debug)]
pub struct NestedVectorPopulator {
    projection_tree: ProjectionTree,
    row_count: usize,
    current_row: usize,
    
    // Accumulators for different data types
    primitive_data: HashMap<String, Vec<JsonValue>>,
    list_accumulators: HashMap<String, ListAccumulator>,
    struct_validity: HashMap<String, Vec<bool>>,
}

impl NestedVectorPopulator {
    /// Create a new nested vector populator
    pub fn new(projection_tree: ProjectionTree, row_count: usize) -> Self {
        Self {
            projection_tree,
            row_count,
            current_row: 0,
            primitive_data: HashMap::new(),
            list_accumulators: HashMap::new(),
            struct_validity: HashMap::new(),
        }
    }

    /// Add row data from the stack parser
    pub fn add_row_data(&mut self, row_data: HashMap<String, JsonValue>) -> Result<(), Box<dyn std::error::Error>> {
        if self.current_row >= self.row_count {
            return Err("Exceeded expected row count".into());
        }

        // Process each field in the row data
        for (path_key, value) in row_data {
            self.add_value_at_path(&path_key, value)?;
        }

        self.current_row += 1;
        Ok(())
    }

    /// Add a value at a specific path
    fn add_value_at_path(&mut self, path_key: &str, value: JsonValue) -> Result<(), Box<dyn std::error::Error>> {
        // For now, store primitive values directly
        // In a full implementation, we'd parse the path and handle nested structures
        if !self.primitive_data.contains_key(path_key) {
            self.primitive_data.insert(path_key.to_string(), Vec::new());
        }
        
        if let Some(values) = self.primitive_data.get_mut(path_key) {
            // Pad with nulls if we're behind
            while values.len() < self.current_row {
                values.push(JsonValue::Null);
            }
            values.push(value);
        }

        Ok(())
    }

    /// Populate DuckDB vectors with accumulated data
    pub fn populate_vectors(&self, output: &mut DataChunkHandle) -> Result<(), Box<dyn std::error::Error>> {
        // Get the column specifications from the projection tree
        let column_specs = self.projection_tree.column_specs();
        
        for (col_idx, spec) in column_specs.iter().enumerate() {
            let path_key = self.path_tokens_to_string(&spec.path_tokens);
            
            if let Some(values) = self.primitive_data.get(&path_key) {
                self.populate_column_vector(output, col_idx, values)?;
            } else {
                // Fill with nulls if no data for this column
                self.populate_null_column(output, col_idx)?;
            }
        }

        Ok(())
    }

    /// Populate a single column vector
    fn populate_column_vector(
        &self,
        output: &mut DataChunkHandle,
        col_idx: usize,
        values: &[JsonValue],
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Determine the vector type and populate accordingly
        match values.first() {
            Some(JsonValue::String(_)) => {
                let vector = output.flat_vector(col_idx);
                for (row_idx, value) in values.iter().enumerate() {
                    match value {
                        JsonValue::String(s) => {
                            vector.insert(row_idx, s.as_str());
                        }
                        JsonValue::Null => {
                            // Set validity to false for null values
                            // Note: This is a simplified approach
                        }
                        _ => {
                            return Err(format!("Type mismatch in column {}: expected string", col_idx).into());
                        }
                    }
                }
            }
            Some(JsonValue::Integer(_)) => {
                let mut vector = output.flat_vector(col_idx);
                let slice = vector.as_mut_slice::<i64>();
                for (row_idx, value) in values.iter().enumerate() {
                    match value {
                        JsonValue::Integer(i) => {
                            slice[row_idx] = *i;
                        }
                        JsonValue::Null => {
                            slice[row_idx] = 0; // Default value, validity should be set separately
                        }
                        _ => {
                            return Err(format!("Type mismatch in column {}: expected integer", col_idx).into());
                        }
                    }
                }
            }
            Some(JsonValue::Float(_)) => {
                let mut vector = output.flat_vector(col_idx);
                let slice = vector.as_mut_slice::<f64>();
                for (row_idx, value) in values.iter().enumerate() {
                    match value {
                        JsonValue::Float(f) => {
                            slice[row_idx] = *f;
                        }
                        JsonValue::Null => {
                            slice[row_idx] = 0.0; // Default value, validity should be set separately
                        }
                        _ => {
                            return Err(format!("Type mismatch in column {}: expected float", col_idx).into());
                        }
                    }
                }
            }
            Some(JsonValue::Boolean(_)) => {
                let mut vector = output.flat_vector(col_idx);
                let slice = vector.as_mut_slice::<bool>();
                for (row_idx, value) in values.iter().enumerate() {
                    match value {
                        JsonValue::Boolean(b) => {
                            slice[row_idx] = *b;
                        }
                        JsonValue::Null => {
                            slice[row_idx] = false; // Default value, validity should be set separately
                        }
                        _ => {
                            return Err(format!("Type mismatch in column {}: expected boolean", col_idx).into());
                        }
                    }
                }
            }
            Some(JsonValue::Array(_)) => {
                // Handle list vectors
                self.populate_list_vector(output, col_idx, values)?;
            }
            Some(JsonValue::Object(_)) => {
                // Handle struct vectors
                self.populate_struct_vector(output, col_idx, values)?;
            }
            Some(JsonValue::Null) | None => {
                // All nulls or no data
                self.populate_null_column(output, col_idx)?;
            }
        }

        Ok(())
    }

    /// Populate a list vector
    fn populate_list_vector(
        &self,
        output: &mut DataChunkHandle,
        col_idx: usize,
        values: &[JsonValue],
    ) -> Result<(), Box<dyn std::error::Error>> {
        let mut list_vector = output.list_vector(col_idx);
        let mut child_offset = 0;

        for (row_idx, value) in values.iter().enumerate() {
            match value {
                JsonValue::Array(arr) => {
                    let array_length = arr.len();
                    list_vector.set_entry(row_idx, child_offset, array_length);
                    
                    // TODO: Populate child vector with array elements
                    // This requires recursive handling of nested types
                    
                    child_offset += array_length;
                }
                JsonValue::Null => {
                    list_vector.set_entry(row_idx, child_offset, 0);
                    // No child elements for null arrays
                }
                _ => {
                    return Err(format!("Type mismatch in column {}: expected array", col_idx).into());
                }
            }
        }

        list_vector.set_len(child_offset);
        Ok(())
    }

    /// Populate a struct vector
    fn populate_struct_vector(
        &self,
        _output: &mut DataChunkHandle,
        _col_idx: usize,
        _values: &[JsonValue],
    ) -> Result<(), Box<dyn std::error::Error>> {
        // TODO: Implement struct vector population
        // This requires handling nested field population
        Err("Struct vector population not yet implemented".into())
    }

    /// Populate a column with all null values
    fn populate_null_column(
        &self,
        _output: &mut DataChunkHandle,
        _col_idx: usize,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // TODO: Implement null column population
        // This requires setting validity bits to false for all rows
        Ok(())
    }

    /// Convert path tokens to a string key
    fn path_tokens_to_string(&self, tokens: &[PathToken]) -> String {
        tokens.iter()
            .map(|token| match token {
                PathToken::Key(key) => key.clone(),
                PathToken::ArrayElem => "[*]".to_string(),
            })
            .collect::<Vec<_>>()
            .join(".")
    }

    /// Get current row count
    pub fn current_row(&self) -> usize {
        self.current_row
    }

    /// Reset for next chunk
    pub fn reset(&mut self) {
        self.current_row = 0;
        self.primitive_data.clear();
        self.list_accumulators.clear();
        self.struct_validity.clear();
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::schema::{JsonSchema, PrimitiveType};
    use indexmap::IndexMap;

    #[test]
    fn test_list_accumulator() {
        let mut accumulator = ListAccumulator::new();

        // Add a list with 3 elements
        let values1 = vec![
            JsonValue::Integer(1),
            JsonValue::Integer(2),
            JsonValue::Integer(3),
        ];
        accumulator.add_list(values1).unwrap();

        // Add a null list
        accumulator.add_null_list();

        // Add another list with 2 elements
        let values2 = vec![
            JsonValue::Integer(4),
            JsonValue::Integer(5),
        ];
        accumulator.add_list(values2).unwrap();

        assert_eq!(accumulator.len(), 3);
        assert_eq!(accumulator.total_child_count, 5);
        assert_eq!(accumulator.offsets, vec![0, 3, 3]);
        assert_eq!(accumulator.lengths, vec![3, 0, 2]);
        assert_eq!(accumulator.validity, vec![true, false, true]);

        // Test memory statistics
        let stats = accumulator.memory_stats();
        assert_eq!(stats.max_list_size_seen, 3);
        assert_eq!(stats.total_lists_processed, 2);
        assert!(stats.total_memory_bytes > 0);
    }

    #[test]
    fn test_list_accumulator_memory_management() {
        let mut accumulator = ListAccumulator::with_capacity(10);

        // Test capacity growth
        let large_list = vec![JsonValue::Integer(1); 20]; // Larger than initial capacity
        accumulator.add_list(large_list).unwrap();

        assert!(accumulator.child_values.capacity() >= 20);
        assert_eq!(accumulator.total_child_count, 20);

        // Test memory optimization
        assert!(!accumulator.is_near_capacity());

        // Test reset
        accumulator.reset(true);
        assert_eq!(accumulator.len(), 0);
        assert_eq!(accumulator.total_child_count, 0);
    }

    #[test]
    fn test_nested_vector_populator_creation() {
        // Create a simple schema
        let mut fields = IndexMap::new();
        fields.insert("name".to_string(), JsonSchema::primitive(PrimitiveType::String));
        fields.insert("age".to_string(), JsonSchema::primitive(PrimitiveType::Integer));
        let schema = JsonSchema::struct_schema(fields);

        // Create projection tree
        let projected_columns = vec![0u64, 1u64];
        let projection_tree = ProjectionTree::new(&schema, &projected_columns).unwrap();

        // Create populator
        let populator = NestedVectorPopulator::new(projection_tree, 100);
        assert_eq!(populator.current_row(), 0);
        assert_eq!(populator.row_count, 100);
    }
}
