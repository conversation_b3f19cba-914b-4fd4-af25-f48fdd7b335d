use crate::projection::PathToken;
use crate::schema::JsonSchema;
use std::fmt;

/// Comprehensive error types for JSON processing
#[derive(Debug, Clone)]
pub enum JsonProcessingError {
    /// Schema validation errors
    SchemaValidation {
        message: String,
        path: Vec<PathToken>,
        expected_type: String,
        actual_type: String,
    },
    /// Type mismatch errors
    TypeMismatch {
        message: String,
        path: Vec<PathToken>,
        expected: String,
        found: String,
    },
    /// Missing required field errors
    MissingField {
        field_name: String,
        path: Vec<PathToken>,
        context: String,
    },
    /// Parsing errors from struson
    ParseError {
        message: String,
        position: Option<String>,
        context: String,
    },
    /// Memory allocation errors
    MemoryError {
        message: String,
        requested_size: usize,
        available_size: Option<usize>,
    },
    /// Vector population errors
    VectorError {
        message: String,
        column_index: usize,
        row_index: usize,
    },
    /// Projection errors
    ProjectionError {
        message: String,
        invalid_path: Vec<PathToken>,
    },
    /// Configuration errors
    ConfigError {
        message: String,
        parameter: String,
    },
    /// Generic processing errors
    ProcessingError {
        message: String,
        context: String,
    },
}

impl fmt::Display for JsonProcessingError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            JsonProcessingError::SchemaValidation { message, path, expected_type, actual_type } => {
                write!(f, "Schema validation error at {}: {}. Expected {}, found {}", 
                       path_to_string(path), message, expected_type, actual_type)
            }
            JsonProcessingError::TypeMismatch { message, path, expected, found } => {
                write!(f, "Type mismatch at {}: {}. Expected {}, found {}", 
                       path_to_string(path), message, expected, found)
            }
            JsonProcessingError::MissingField { field_name, path, context } => {
                write!(f, "Missing required field '{}' at {} ({})", 
                       field_name, path_to_string(path), context)
            }
            JsonProcessingError::ParseError { message, position, context } => {
                if let Some(pos) = position {
                    write!(f, "Parse error at {}: {} ({})", pos, message, context)
                } else {
                    write!(f, "Parse error: {} ({})", message, context)
                }
            }
            JsonProcessingError::MemoryError { message, requested_size, available_size } => {
                if let Some(available) = available_size {
                    write!(f, "Memory error: {}. Requested {} bytes, available {} bytes", 
                           message, requested_size, available)
                } else {
                    write!(f, "Memory error: {}. Requested {} bytes", message, requested_size)
                }
            }
            JsonProcessingError::VectorError { message, column_index, row_index } => {
                write!(f, "Vector error at column {}, row {}: {}", 
                       column_index, row_index, message)
            }
            JsonProcessingError::ProjectionError { message, invalid_path } => {
                write!(f, "Projection error at {}: {}", 
                       path_to_string(invalid_path), message)
            }
            JsonProcessingError::ConfigError { message, parameter } => {
                write!(f, "Configuration error for parameter '{}': {}", parameter, message)
            }
            JsonProcessingError::ProcessingError { message, context } => {
                write!(f, "Processing error: {} ({})", message, context)
            }
        }
    }
}

impl std::error::Error for JsonProcessingError {}

/// Error handling strategy
#[derive(Debug, Clone, PartialEq)]
pub enum ErrorHandlingMode {
    /// Strict mode: fail immediately on any error
    Strict,
    /// Lenient mode: log errors and continue with defaults
    Lenient,
    /// Collect mode: collect all errors and report at end
    Collect,
}

/// Error context for tracking error location and details
#[derive(Debug, Clone)]
pub struct ErrorContext {
    pub current_path: Vec<PathToken>,
    pub current_row: usize,
    pub current_column: usize,
    pub schema_context: Option<JsonSchema>,
    pub processing_stage: ProcessingStage,
}

/// Processing stage for error context
#[derive(Debug, Clone, PartialEq)]
pub enum ProcessingStage {
    SchemaInference,
    Parsing,
    VectorPopulation,
    Validation,
}

/// Error collector for batch error handling
#[derive(Debug)]
pub struct ErrorCollector {
    errors: Vec<JsonProcessingError>,
    warnings: Vec<String>,
    mode: ErrorHandlingMode,
    max_errors: usize,
    context: ErrorContext,
}

impl ErrorCollector {
    /// Create a new error collector
    pub fn new(mode: ErrorHandlingMode) -> Self {
        Self {
            errors: Vec::new(),
            warnings: Vec::new(),
            mode,
            max_errors: 100, // Default limit
            context: ErrorContext {
                current_path: Vec::new(),
                current_row: 0,
                current_column: 0,
                schema_context: None,
                processing_stage: ProcessingStage::Parsing,
            },
        }
    }

    /// Add an error to the collector
    pub fn add_error(&mut self, error: JsonProcessingError) -> Result<(), JsonProcessingError> {
        match self.mode {
            ErrorHandlingMode::Strict => {
                // Fail immediately
                Err(error)
            }
            ErrorHandlingMode::Lenient => {
                // Log and continue
                self.warnings.push(format!("Error (continuing): {}", error));
                Ok(())
            }
            ErrorHandlingMode::Collect => {
                // Collect for later reporting
                self.errors.push(error);
                
                // Check if we've hit the error limit
                if self.errors.len() >= self.max_errors {
                    Err(JsonProcessingError::ProcessingError {
                        message: format!("Too many errors collected: {}", self.errors.len()),
                        context: "Error limit exceeded".to_string(),
                    })
                } else {
                    Ok(())
                }
            }
        }
    }

    /// Add a warning
    pub fn add_warning(&mut self, message: String) {
        self.warnings.push(message);
    }

    /// Update the current context
    pub fn update_context(&mut self, context: ErrorContext) {
        self.context = context;
    }

    /// Set current path
    pub fn set_path(&mut self, path: Vec<PathToken>) {
        self.context.current_path = path;
    }

    /// Set current row
    pub fn set_row(&mut self, row: usize) {
        self.context.current_row = row;
    }

    /// Set processing stage
    pub fn set_stage(&mut self, stage: ProcessingStage) {
        self.context.processing_stage = stage;
    }

    /// Get collected errors
    pub fn errors(&self) -> &[JsonProcessingError] {
        &self.errors
    }

    /// Get warnings
    pub fn warnings(&self) -> &[String] {
        &self.warnings
    }

    /// Check if there are any errors
    pub fn has_errors(&self) -> bool {
        !self.errors.is_empty()
    }

    /// Get error summary
    pub fn error_summary(&self) -> ErrorSummary {
        ErrorSummary {
            total_errors: self.errors.len(),
            total_warnings: self.warnings.len(),
            error_types: self.categorize_errors(),
            most_common_error: self.most_common_error_type(),
        }
    }

    /// Categorize errors by type
    fn categorize_errors(&self) -> std::collections::HashMap<String, usize> {
        let mut categories = std::collections::HashMap::new();
        
        for error in &self.errors {
            let category = match error {
                JsonProcessingError::SchemaValidation { .. } => "Schema Validation",
                JsonProcessingError::TypeMismatch { .. } => "Type Mismatch",
                JsonProcessingError::MissingField { .. } => "Missing Field",
                JsonProcessingError::ParseError { .. } => "Parse Error",
                JsonProcessingError::MemoryError { .. } => "Memory Error",
                JsonProcessingError::VectorError { .. } => "Vector Error",
                JsonProcessingError::ProjectionError { .. } => "Projection Error",
                JsonProcessingError::ConfigError { .. } => "Config Error",
                JsonProcessingError::ProcessingError { .. } => "Processing Error",
            };
            
            *categories.entry(category.to_string()).or_insert(0) += 1;
        }
        
        categories
    }

    /// Find the most common error type
    fn most_common_error_type(&self) -> Option<String> {
        let categories = self.categorize_errors();
        categories.into_iter()
            .max_by_key(|(_, count)| *count)
            .map(|(category, _)| category)
    }

    /// Clear all errors and warnings
    pub fn clear(&mut self) {
        self.errors.clear();
        self.warnings.clear();
    }

    /// Set maximum number of errors to collect
    pub fn set_max_errors(&mut self, max: usize) {
        self.max_errors = max;
    }
}

/// Summary of errors collected
#[derive(Debug, Clone)]
pub struct ErrorSummary {
    pub total_errors: usize,
    pub total_warnings: usize,
    pub error_types: std::collections::HashMap<String, usize>,
    pub most_common_error: Option<String>,
}

/// Utility functions for error handling
pub mod utils {
    use super::*;

    /// Create a schema validation error
    pub fn schema_validation_error(
        path: Vec<PathToken>,
        expected: &str,
        actual: &str,
        message: &str,
    ) -> JsonProcessingError {
        JsonProcessingError::SchemaValidation {
            message: message.to_string(),
            path,
            expected_type: expected.to_string(),
            actual_type: actual.to_string(),
        }
    }

    /// Create a type mismatch error
    pub fn type_mismatch_error(
        path: Vec<PathToken>,
        expected: &str,
        found: &str,
    ) -> JsonProcessingError {
        JsonProcessingError::TypeMismatch {
            message: "Type mismatch detected".to_string(),
            path,
            expected: expected.to_string(),
            found: found.to_string(),
        }
    }

    /// Create a missing field error
    pub fn missing_field_error(
        field_name: &str,
        path: Vec<PathToken>,
        context: &str,
    ) -> JsonProcessingError {
        JsonProcessingError::MissingField {
            field_name: field_name.to_string(),
            path,
            context: context.to_string(),
        }
    }

    /// Validate error handling configuration
    pub fn validate_error_config(mode: &ErrorHandlingMode, max_errors: usize) -> Result<(), String> {
        match mode {
            ErrorHandlingMode::Collect if max_errors == 0 => {
                Err("Max errors must be greater than 0 in collect mode".to_string())
            }
            _ => Ok(()),
        }
    }
}

/// Convert path tokens to string representation
fn path_to_string(path: &[PathToken]) -> String {
    if path.is_empty() {
        return "root".to_string();
    }
    
    path.iter()
        .map(|token| match token {
            PathToken::Key(key) => key.clone(),
            PathToken::ArrayElem => "[*]".to_string(),
        })
        .collect::<Vec<_>>()
        .join(".")
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_creation() {
        let path = vec![PathToken::Key("user".to_string()), PathToken::Key("name".to_string())];
        
        let error = utils::schema_validation_error(
            path.clone(),
            "string",
            "number",
            "Invalid type for name field"
        );
        
        match error {
            JsonProcessingError::SchemaValidation { message, path: error_path, expected_type, actual_type } => {
                assert_eq!(message, "Invalid type for name field");
                assert_eq!(error_path, path);
                assert_eq!(expected_type, "string");
                assert_eq!(actual_type, "number");
            }
            _ => panic!("Expected SchemaValidation error"),
        }
    }

    #[test]
    fn test_error_collector_strict_mode() {
        let mut collector = ErrorCollector::new(ErrorHandlingMode::Strict);
        
        let error = utils::type_mismatch_error(
            vec![PathToken::Key("test".to_string())],
            "string",
            "number"
        );
        
        let result = collector.add_error(error);
        assert!(result.is_err());
    }

    #[test]
    fn test_error_collector_lenient_mode() {
        let mut collector = ErrorCollector::new(ErrorHandlingMode::Lenient);
        
        let error = utils::type_mismatch_error(
            vec![PathToken::Key("test".to_string())],
            "string",
            "number"
        );
        
        let result = collector.add_error(error);
        assert!(result.is_ok());
        assert_eq!(collector.warnings().len(), 1);
    }

    #[test]
    fn test_error_collector_collect_mode() {
        let mut collector = ErrorCollector::new(ErrorHandlingMode::Collect);
        
        let error1 = utils::type_mismatch_error(
            vec![PathToken::Key("test1".to_string())],
            "string",
            "number"
        );
        
        let error2 = utils::missing_field_error(
            "required_field",
            vec![PathToken::Key("test2".to_string())],
            "validation"
        );
        
        collector.add_error(error1).unwrap();
        collector.add_error(error2).unwrap();
        
        assert_eq!(collector.errors().len(), 2);
        assert!(collector.has_errors());
        
        let summary = collector.error_summary();
        assert_eq!(summary.total_errors, 2);
        assert!(summary.error_types.contains_key("Type Mismatch"));
        assert!(summary.error_types.contains_key("Missing Field"));
    }

    #[test]
    fn test_path_to_string() {
        let path = vec![
            PathToken::Key("user".to_string()),
            PathToken::ArrayElem,
            PathToken::Key("name".to_string()),
        ];
        
        assert_eq!(path_to_string(&path), "user.[*].name");
        assert_eq!(path_to_string(&[]), "root");
    }
}
