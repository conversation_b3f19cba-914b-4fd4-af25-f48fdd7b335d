use crate::schema::{JsonSchema, PrimitiveType};
use duckdb::core::{<PERSON>Vector, ListVector, StructVector, Inserter};
// use struson::reader::{JsonReader, ValueType};

/// Handles population of DuckDB vectors from JSON data
pub struct VectorPopulator {
    schema: JsonSchema,
    row_count: usize,
    current_row: usize,
}

/// Represents a JSON value during processing
#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum JsonValue {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>(bool),
    Integer(i64),
    Float(f64),
    String(String),
    Object(std::collections::HashMap<String, JsonValue>),
    Array(Vec<JsonValue>),
}

impl VectorPopulator {
    /// Create a new vector populator with the given schema
    pub fn new(schema: JsonSchema, row_count: usize) -> Self {
        Self {
            schema,
            row_count,
            current_row: 0,
        }
    }

    /// Populate a struct vector with JSON object data
    pub fn populate_struct_vector(
        &self,
        _vector: &mut StructVector,
        _data: &JsonValue,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // TODO: Implement struct vector population
        // This is a complex operation that requires careful handling of DuckDB's vector API
        Ok(())
    }

    /// Populate a list vector with JSON array data
    pub fn populate_list_vector(
        &mut self,
        vector: &mut ListVector,
        data: &JsonValue,
    ) -> Result<(), Box<dyn std::error::Error>> {
        if let JsonSchema::List { element_schema, .. } = &self.schema {
            if let JsonValue::Array(arr) = data {
                let start_offset = vector.len();
                let array_length = arr.len();
                
                // Reserve space for the array elements (reserve is private, we'll use the child method)
                
                // Set the list entry for this row
                vector.set_entry(self.current_row, start_offset, array_length);
                
                // Populate child elements
                match element_schema.as_ref() {
                    JsonSchema::Primitive(ptype) => {
                        let mut child_vector = vector.child(start_offset + array_length);
                        for (i, element) in arr.iter().enumerate() {
                            self.populate_primitive_at_index(&mut child_vector, element, ptype, start_offset + i)?;
                        }
                    }
                    JsonSchema::Struct { .. } => {
                        let mut child_vector = vector.struct_child(start_offset + array_length);
                        let mut child_populator = VectorPopulator::new(element_schema.as_ref().clone(), array_length);
                        
                        for (i, element) in arr.iter().enumerate() {
                            child_populator.current_row = start_offset + i;
                            child_populator.populate_struct_vector(&mut child_vector, element)?;
                        }
                    }
                    JsonSchema::List { .. } => {
                        let mut child_vector = vector.list_child();
                        let mut child_populator = VectorPopulator::new(element_schema.as_ref().clone(), array_length);
                        
                        for (i, element) in arr.iter().enumerate() {
                            child_populator.current_row = start_offset + i;
                            child_populator.populate_list_vector(&mut child_vector, element)?;
                        }
                    }
                }
                
                // Update the vector size
                vector.set_len(start_offset + array_length);
            } else {
                // Handle non-array data in list context (set to null)
                vector.set_null(self.current_row);
            }
        }
        
        Ok(())
    }

    /// Populate a primitive vector with JSON primitive data
    pub fn populate_primitive_vector(
        &mut self,
        vector: &mut FlatVector,
        data: &JsonValue,
        ptype: &PrimitiveType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        self.populate_primitive_at_index(vector, data, ptype, self.current_row)
    }

    /// Populate a primitive vector at a specific index
    fn populate_primitive_at_index(
        &self,
        vector: &mut FlatVector,
        data: &JsonValue,
        ptype: &PrimitiveType,
        index: usize,
    ) -> Result<(), Box<dyn std::error::Error>> {
        match (data, ptype) {
            (JsonValue::Null, _) => {
                vector.set_null(index);
            }
            (JsonValue::Boolean(b), PrimitiveType::Boolean) => {
                let slice = vector.as_mut_slice::<bool>();
                slice[index] = *b;
            }
            (JsonValue::Integer(i), PrimitiveType::Integer) => {
                let slice = vector.as_mut_slice::<i64>();
                slice[index] = *i;
            }
            (JsonValue::Float(f), PrimitiveType::Float) => {
                let slice = vector.as_mut_slice::<f64>();
                slice[index] = *f;
            }
            (JsonValue::String(s), PrimitiveType::String) => {
                vector.insert(index, s.as_str());
            }
            // Type coercion cases
            (JsonValue::Integer(i), PrimitiveType::Float) => {
                let slice = vector.as_mut_slice::<f64>();
                slice[index] = *i as f64;
            }
            (JsonValue::Boolean(b), PrimitiveType::String) => {
                let s = if *b { "true" } else { "false" };
                vector.insert(index, s);
            }
            (JsonValue::Integer(i), PrimitiveType::String) => {
                let s = i.to_string();
                vector.insert(index, s.as_str());
            }
            (JsonValue::Float(f), PrimitiveType::String) => {
                let s = f.to_string();
                vector.insert(index, s.as_str());
            }
            // Complex types to string (fallback)
            (JsonValue::Object(_), PrimitiveType::String) => {
                let s = serde_json::to_string(data)?;
                vector.insert(index, s.as_str());
            }
            (JsonValue::Array(_), PrimitiveType::String) => {
                let s = serde_json::to_string(data)?;
                vector.insert(index, s.as_str());
            }
            // Invalid combinations - set to null
            _ => {
                vector.set_null(index);
            }
        }
        
        Ok(())
    }

    /// Advance to the next row
    pub fn next_row(&mut self) {
        self.current_row += 1;
    }

    /// Get current row index
    pub fn current_row(&self) -> usize {
        self.current_row
    }

    /// Reset to first row
    pub fn reset(&mut self) {
        self.current_row = 0;
    }
}

/// Convert struson JsonReader value to our JsonValue representation
/// This is a simplified version for now - will be expanded later
pub fn read_json_value_simple() -> Result<JsonValue, Box<dyn std::error::Error>> {
    // TODO: Implement proper JSON value reading from struson
    Ok(JsonValue::Null)
}

// Temporary serde_json implementation for fallback string conversion
mod serde_json {
    use super::JsonValue;
    
    pub fn to_string(value: &JsonValue) -> Result<String, Box<dyn std::error::Error>> {
        match value {
            JsonValue::Null => Ok("null".to_string()),
            JsonValue::Boolean(b) => Ok(b.to_string()),
            JsonValue::Integer(i) => Ok(i.to_string()),
            JsonValue::Float(f) => Ok(f.to_string()),
            JsonValue::String(s) => Ok(format!("\"{}\"", s.replace('"', "\\\""))),
            JsonValue::Object(obj) => {
                let pairs: Vec<String> = obj.iter()
                    .map(|(k, v)| format!("\"{}\":{}", k.replace('"', "\\\""), to_string(v).unwrap_or_default()))
                    .collect();
                Ok(format!("{{{}}}", pairs.join(",")))
            }
            JsonValue::Array(arr) => {
                let elements: Vec<String> = arr.iter()
                    .map(|v| to_string(v).unwrap_or_default())
                    .collect();
                Ok(format!("[{}]", elements.join(",")))
            }
        }
    }
}
