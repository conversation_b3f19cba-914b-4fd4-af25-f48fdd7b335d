use crate::projection::{PathToken, ProjectionTree, ProjNode};
use crate::schema::JsonSchema;
use crate::vector_populator::JsonValue;
use std::collections::{HashMap, HashSet};

/// Manages null handling and missing field tracking for JSON parsing
#[derive(Debug, Clone)]
pub struct NullHandler {
    /// Expected fields at each path level
    expected_fields: HashMap<String, HashSet<String>>,
    /// Default values for missing fields
    default_values: HashMap<String, JsonValue>,
    /// Null handling strategy
    strategy: NullHandlingStrategy,
    /// Track which fields have been seen in current row
    seen_fields: HashMap<String, bool>,
    /// Track current parsing path
    current_path: Vec<PathToken>,
}

/// Strategy for handling null and missing values
#[derive(Debug, Clone, PartialEq)]
pub enum NullHandlingStrategy {
    /// Strict mode: fail on missing required fields
    Strict,
    /// Lenient mode: use defaults for missing fields
    Lenient,
    /// Fill mode: always fill missing fields with defaults
    FillDefaults,
}

/// Validity bitmap manager for DuckDB vectors
#[derive(Debug)]
pub struct ValidityBitmap {
    /// Bitmap data (true = valid, false = null)
    bits: Vec<bool>,
    /// Current row being processed
    current_row: usize,
    /// Total capacity
    capacity: usize,
}

impl ValidityBitmap {
    /// Create a new validity bitmap
    pub fn new(capacity: usize) -> Self {
        Self {
            bits: vec![true; capacity], // Default to all valid
            current_row: 0,
            capacity,
        }
    }

    /// Mark current row as null
    pub fn set_null(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        if self.current_row >= self.capacity {
            return Err("Validity bitmap capacity exceeded".into());
        }
        self.bits[self.current_row] = false;
        Ok(())
    }

    /// Mark current row as valid
    pub fn set_valid(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        if self.current_row >= self.capacity {
            return Err("Validity bitmap capacity exceeded".into());
        }
        self.bits[self.current_row] = true;
        Ok(())
    }

    /// Move to next row
    pub fn next_row(&mut self) {
        self.current_row += 1;
    }

    /// Reset to beginning
    pub fn reset(&mut self) {
        self.current_row = 0;
        self.bits.fill(true); // Reset all to valid
    }

    /// Get validity for a specific row
    pub fn is_valid(&self, row: usize) -> bool {
        self.bits.get(row).copied().unwrap_or(false)
    }

    /// Get the raw bitmap data
    pub fn bits(&self) -> &[bool] {
        &self.bits
    }

    /// Get current row
    pub fn current_row(&self) -> usize {
        self.current_row
    }
}

impl NullHandler {
    /// Create a new null handler
    pub fn new(strategy: NullHandlingStrategy) -> Self {
        Self {
            expected_fields: HashMap::new(),
            default_values: HashMap::new(),
            strategy,
            seen_fields: HashMap::new(),
            current_path: Vec::new(),
        }
    }

    /// Initialize from projection tree and schema
    pub fn from_projection_tree(
        projection_tree: &ProjectionTree,
        schema: &JsonSchema,
        strategy: NullHandlingStrategy,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        let mut handler = Self::new(strategy);
        handler.build_expected_fields(projection_tree.root(), schema, &[])?;
        handler.build_default_values(schema, &[])?;
        Ok(handler)
    }

    /// Build expected fields map from projection tree
    fn build_expected_fields(
        &mut self,
        proj_node: &ProjNode,
        schema: &JsonSchema,
        current_path: &[PathToken],
    ) -> Result<(), Box<dyn std::error::Error>> {
        match (proj_node, schema) {
            (ProjNode::Object { children, .. }, JsonSchema::Struct { fields, .. }) => {
                let path_key = self.path_to_string(current_path);
                let mut expected = HashSet::new();
                
                for field_name in children.keys() {
                    expected.insert(field_name.clone());
                    
                    // Recursively process child fields
                    if let (Some(child_node), Some(child_schema)) = 
                        (children.get(field_name), fields.get(field_name)) {
                        let mut new_path = current_path.to_vec();
                        new_path.push(PathToken::Key(field_name.clone()));
                        self.build_expected_fields(child_node, child_schema, &new_path)?;
                    }
                }
                
                self.expected_fields.insert(path_key, expected);
            }
            (ProjNode::List { elem, .. }, JsonSchema::List { element_schema, .. }) => {
                let mut new_path = current_path.to_vec();
                new_path.push(PathToken::ArrayElem);
                self.build_expected_fields(elem, element_schema, &new_path)?;
            }
            _ => {
                // Leaf nodes or mismatched types - no expected fields
            }
        }
        
        Ok(())
    }

    /// Build default values for missing fields
    fn build_default_values(
        &mut self,
        schema: &JsonSchema,
        current_path: &[PathToken],
    ) -> Result<(), Box<dyn std::error::Error>> {
        match schema {
            JsonSchema::Primitive(ptype) => {
                let path_key = self.path_to_string(current_path);
                let default_value = self.get_default_for_primitive(ptype);
                self.default_values.insert(path_key, default_value);
            }
            JsonSchema::Struct { fields, .. } => {
                for (field_name, field_schema) in fields {
                    let mut new_path = current_path.to_vec();
                    new_path.push(PathToken::Key(field_name.clone()));
                    self.build_default_values(field_schema, &new_path)?;
                }
            }
            JsonSchema::List { element_schema, .. } => {
                let mut new_path = current_path.to_vec();
                new_path.push(PathToken::ArrayElem);
                self.build_default_values(element_schema, &new_path)?;
                
                // Also add default for the list itself
                let path_key = self.path_to_string(current_path);
                self.default_values.insert(path_key, JsonValue::Array(Vec::new()));
            }
        }
        
        Ok(())
    }

    /// Get default value for a primitive type
    pub fn get_default_for_primitive(&self, ptype: &crate::schema::PrimitiveType) -> JsonValue {
        use crate::schema::PrimitiveType;
        match ptype {
            PrimitiveType::String => JsonValue::String(String::new()),
            PrimitiveType::Integer => JsonValue::Integer(0),
            PrimitiveType::Float => JsonValue::Float(0.0),
            PrimitiveType::Boolean => JsonValue::Boolean(false),
            PrimitiveType::Null => JsonValue::Null,
        }
    }

    /// Start processing a new row
    pub fn start_row(&mut self) {
        self.seen_fields.clear();
        self.current_path.clear();
    }

    /// Mark a field as seen
    pub fn mark_field_seen(&mut self, path: &[PathToken]) {
        let path_key = self.path_to_string(path);
        self.seen_fields.insert(path_key, true);
    }

    /// Check if a field was seen
    pub fn was_field_seen(&self, path: &[PathToken]) -> bool {
        let path_key = self.path_to_string(path);
        self.seen_fields.get(&path_key).copied().unwrap_or(false)
    }

    /// Finalize row and handle missing fields
    pub fn finalize_row(&mut self) -> Result<Vec<(String, JsonValue)>, Box<dyn std::error::Error>> {
        let mut missing_fields = Vec::new();
        
        // Check all expected fields
        for (path_key, expected_fields) in &self.expected_fields {
            for field_name in expected_fields {
                let field_path_key = if path_key.is_empty() {
                    field_name.clone()
                } else {
                    format!("{}.{}", path_key, field_name)
                };
                
                if !self.seen_fields.contains_key(&field_path_key) {
                    // Field is missing
                    match self.strategy {
                        NullHandlingStrategy::Strict => {
                            return Err(format!("Required field '{}' is missing", field_path_key).into());
                        }
                        NullHandlingStrategy::Lenient | NullHandlingStrategy::FillDefaults => {
                            if let Some(default_value) = self.default_values.get(&field_path_key) {
                                missing_fields.push((field_path_key, default_value.clone()));
                            } else {
                                missing_fields.push((field_path_key, JsonValue::Null));
                            }
                        }
                    }
                }
            }
        }
        
        Ok(missing_fields)
    }

    /// Convert path tokens to string key
    fn path_to_string(&self, path: &[PathToken]) -> String {
        path.iter()
            .map(|token| match token {
                PathToken::Key(key) => key.clone(),
                PathToken::ArrayElem => "[*]".to_string(),
            })
            .collect::<Vec<_>>()
            .join(".")
    }

    /// Get null handling strategy
    pub fn strategy(&self) -> &NullHandlingStrategy {
        &self.strategy
    }

    /// Set null handling strategy
    pub fn set_strategy(&mut self, strategy: NullHandlingStrategy) {
        self.strategy = strategy;
    }

    /// Check if a field is required based on strategy
    pub fn is_field_required(&self, path: &[PathToken]) -> bool {
        match self.strategy {
            NullHandlingStrategy::Strict => true,
            NullHandlingStrategy::Lenient | NullHandlingStrategy::FillDefaults => false,
        }
    }

    /// Get default value for a path
    pub fn get_default_value(&self, path: &[PathToken]) -> Option<&JsonValue> {
        let path_key = self.path_to_string(path);
        self.default_values.get(&path_key)
    }
}

/// Utility functions for null handling
pub mod utils {
    use super::*;

    /// Create a null handler for testing
    pub fn create_test_null_handler() -> NullHandler {
        NullHandler::new(NullHandlingStrategy::Lenient)
    }

    /// Validate null handling configuration
    pub fn validate_null_config(
        projection_tree: &ProjectionTree,
        schema: &JsonSchema,
        strategy: &NullHandlingStrategy,
    ) -> Result<(), String> {
        // Basic validation - ensure strategy is compatible with schema
        match strategy {
            NullHandlingStrategy::Strict => {
                // In strict mode, all projected fields must be present
                // This is validated at runtime
                Ok(())
            }
            NullHandlingStrategy::Lenient | NullHandlingStrategy::FillDefaults => {
                // These modes are always valid
                Ok(())
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::schema::{JsonSchema, PrimitiveType};
    use indexmap::IndexMap;

    #[test]
    fn test_validity_bitmap() {
        let mut bitmap = ValidityBitmap::new(5);
        
        // All should start as valid
        assert!(bitmap.is_valid(0));
        assert!(bitmap.is_valid(1));
        
        // Mark first row as null
        bitmap.set_null().unwrap();
        bitmap.next_row();
        
        // Mark second row as valid (explicitly)
        bitmap.set_valid().unwrap();
        bitmap.next_row();
        
        // Check results
        assert!(!bitmap.is_valid(0)); // First row is null
        assert!(bitmap.is_valid(1));  // Second row is valid
        assert!(bitmap.is_valid(2));  // Third row is valid (default)
    }

    #[test]
    fn test_null_handler_creation() {
        let handler = NullHandler::new(NullHandlingStrategy::Lenient);
        assert_eq!(handler.strategy(), &NullHandlingStrategy::Lenient);
    }

    #[test]
    fn test_field_tracking() {
        let mut handler = NullHandler::new(NullHandlingStrategy::Lenient);
        
        handler.start_row();
        
        let path = vec![PathToken::Key("name".to_string())];
        assert!(!handler.was_field_seen(&path));
        
        handler.mark_field_seen(&path);
        assert!(handler.was_field_seen(&path));
    }

    #[test]
    fn test_default_values() {
        let handler = NullHandler::new(NullHandlingStrategy::FillDefaults);
        
        // Test primitive defaults
        assert_eq!(
            handler.get_default_for_primitive(&PrimitiveType::String),
            JsonValue::String(String::new())
        );
        assert_eq!(
            handler.get_default_for_primitive(&PrimitiveType::Integer),
            JsonValue::Integer(0)
        );
        assert_eq!(
            handler.get_default_for_primitive(&PrimitiveType::Float),
            JsonValue::Float(0.0)
        );
        assert_eq!(
            handler.get_default_for_primitive(&PrimitiveType::Boolean),
            JsonValue::Boolean(false)
        );
    }
}
