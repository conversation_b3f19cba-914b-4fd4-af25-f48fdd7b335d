use crate::projection::{ProjectionTree, PathToken, ProjNode};
use crate::stack_parser::StackBasedParser;
use crate::nested_vectors::NestedVectorPopulator;
use crate::schema::JsonSchema;
use crate::vector_populator::JsonValue;
use duckdb::core::DataChunkHandle;
use std::collections::HashMap;
use std::io::Read;

/// Integrated projection-aware JSON processor
pub struct ProjectionAwareProcessor<R: Read> {
    parser: StackBasedParser<R>,
    vector_populator: NestedVectorPopulator,
    projection_tree: ProjectionTree,
    chunk_size: usize,
}

impl<R: Read> ProjectionAwareProcessor<R> {
    /// Create a new projection-aware processor
    pub fn new(
        reader: R,
        schema: &JsonSchema,
        projected_columns: &[u64],
        chunk_size: usize,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        // Create projection tree
        let projection_tree = ProjectionTree::new(schema, projected_columns)?;
        
        // Create stack-based parser with projection tree
        let parser = StackBasedParser::new(reader, projection_tree.clone());
        
        // Create vector populator
        let vector_populator = NestedVectorPopulator::new(projection_tree.clone(), chunk_size);
        
        Ok(Self {
            parser,
            vector_populator,
            projection_tree,
            chunk_size,
        })
    }

    /// Process the next chunk of data
    pub fn process_chunk(&mut self, output: &mut DataChunkHandle) -> Result<usize, Box<dyn std::error::Error>> {
        let mut rows_processed = 0;
        
        // Reset vector populator for new chunk
        self.vector_populator.reset();
        
        // Parse rows until chunk is full or no more data
        while rows_processed < self.chunk_size {
            match self.parser.parse_next_row()? {
                Some(row_data) => {
                    // Add row data to vector populator
                    self.vector_populator.add_row_data(row_data)?;
                    rows_processed += 1;
                }
                None => {
                    // No more data
                    break;
                }
            }
        }
        
        // Populate DuckDB vectors with accumulated data
        if rows_processed > 0 {
            self.vector_populator.populate_vectors(output)?;
        }
        
        Ok(rows_processed)
    }

    /// Get projection statistics
    pub fn get_projection_stats(&self) -> ProjectionStats {
        ProjectionStats {
            total_columns: self.projection_tree.total_columns(),
            projected_columns: self.projection_tree.column_specs().len(),
            projection_ratio: self.projection_tree.column_specs().len() as f64 
                / self.projection_tree.total_columns() as f64,
        }
    }

    /// Check if a specific path should be parsed
    pub fn should_parse_path(&self, path: &[PathToken]) -> bool {
        self.projection_tree.should_parse_path(path)
    }

    /// Get the projection tree (for debugging/inspection)
    pub fn projection_tree(&self) -> &ProjectionTree {
        &self.projection_tree
    }
}

/// Statistics about projection effectiveness
#[derive(Debug, Clone)]
pub struct ProjectionStats {
    pub total_columns: usize,
    pub projected_columns: usize,
    pub projection_ratio: f64,
}

/// Enhanced stack parser that integrates with projection tree
pub struct ProjectionAwareStackParser<R: Read> {
    base_parser: StackBasedParser<R>,
    projection_tree: ProjectionTree,
    current_path: Vec<PathToken>,
    skip_depth: usize, // Track how deep we are in skipped content
}

impl<R: Read> ProjectionAwareStackParser<R> {
    /// Create a new projection-aware stack parser
    pub fn new(reader: R, projection_tree: ProjectionTree) -> Self {
        let base_parser = StackBasedParser::new(reader, projection_tree.clone());
        
        Self {
            base_parser,
            projection_tree,
            current_path: Vec::new(),
            skip_depth: 0,
        }
    }

    /// Parse next row with projection awareness
    pub fn parse_next_row(&mut self) -> Result<Option<HashMap<String, JsonValue>>, Box<dyn std::error::Error>> {
        // Reset path tracking
        self.current_path.clear();
        self.skip_depth = 0;
        
        // Use the base parser but with enhanced path tracking
        self.base_parser.parse_next_row()
    }

    /// Check if we should parse the current path
    fn should_parse_current_path(&self) -> bool {
        if self.skip_depth > 0 {
            return false; // We're inside skipped content
        }
        
        self.projection_tree.should_parse_path(&self.current_path)
    }

    /// Enter a new path level
    fn enter_path(&mut self, token: PathToken) {
        self.current_path.push(token);
        
        // Check if we should start skipping
        if !self.should_parse_current_path() && self.skip_depth == 0 {
            self.skip_depth = 1;
        }
    }

    /// Exit current path level
    fn exit_path(&mut self) {
        if let Some(_) = self.current_path.pop() {
            // If we were skipping and we're back to a level that should be parsed
            if self.skip_depth > 0 {
                self.skip_depth -= 1;
                if self.skip_depth == 0 && !self.should_parse_current_path() {
                    // Still shouldn't parse, continue skipping
                    self.skip_depth = 1;
                }
            }
        }
    }

    /// Get current parsing statistics
    pub fn get_parsing_stats(&self) -> ParsingStats {
        ParsingStats {
            current_path: self.current_path.clone(),
            skip_depth: self.skip_depth,
            is_skipping: self.skip_depth > 0,
            rows_processed: self.base_parser.rows_processed(),
        }
    }
}

/// Statistics about parsing progress
#[derive(Debug, Clone)]
pub struct ParsingStats {
    pub current_path: Vec<PathToken>,
    pub skip_depth: usize,
    pub is_skipping: bool,
    pub rows_processed: usize,
}

/// Utility functions for projection integration
pub mod utils {
    use super::*;
    use crate::schema::{JsonSchema, PrimitiveType};
    use indexmap::IndexMap;

    /// Create a simple projection tree for testing
    pub fn create_test_projection_tree() -> Result<ProjectionTree, Box<dyn std::error::Error>> {
        let mut fields = IndexMap::new();
        fields.insert("name".to_string(), JsonSchema::primitive(PrimitiveType::String));
        fields.insert("age".to_string(), JsonSchema::primitive(PrimitiveType::Integer));
        fields.insert("email".to_string(), JsonSchema::primitive(PrimitiveType::String));
        
        let schema = JsonSchema::struct_schema(fields);
        let projected_columns = vec![0u64, 2u64]; // Project "name" and "email", skip "age"
        
        ProjectionTree::new(&schema, &projected_columns)
    }

    /// Analyze projection effectiveness
    pub fn analyze_projection_effectiveness(
        total_fields: usize,
        projected_fields: usize,
        estimated_data_size: usize,
    ) -> ProjectionAnalysis {
        let projection_ratio = projected_fields as f64 / total_fields as f64;
        let estimated_savings = estimated_data_size as f64 * (1.0 - projection_ratio);
        
        ProjectionAnalysis {
            total_fields,
            projected_fields,
            projection_ratio,
            estimated_data_size,
            estimated_savings: estimated_savings as usize,
            efficiency_rating: if projection_ratio < 0.3 {
                EfficiencyRating::Excellent
            } else if projection_ratio < 0.6 {
                EfficiencyRating::Good
            } else if projection_ratio < 0.8 {
                EfficiencyRating::Fair
            } else {
                EfficiencyRating::Poor
            },
        }
    }

    /// Projection analysis results
    #[derive(Debug, Clone)]
    pub struct ProjectionAnalysis {
        pub total_fields: usize,
        pub projected_fields: usize,
        pub projection_ratio: f64,
        pub estimated_data_size: usize,
        pub estimated_savings: usize,
        pub efficiency_rating: EfficiencyRating,
    }

    /// Efficiency rating for projection
    #[derive(Debug, Clone, PartialEq)]
    pub enum EfficiencyRating {
        Excellent, // < 30% of fields projected
        Good,      // 30-60% of fields projected
        Fair,      // 60-80% of fields projected
        Poor,      // > 80% of fields projected
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::schema::{JsonSchema, PrimitiveType};
    use indexmap::IndexMap;
    use std::io::Cursor;

    #[test]
    fn test_projection_aware_processor_creation() {
        // Create test schema
        let mut fields = IndexMap::new();
        fields.insert("name".to_string(), JsonSchema::primitive(PrimitiveType::String));
        fields.insert("age".to_string(), JsonSchema::primitive(PrimitiveType::Integer));
        fields.insert("email".to_string(), JsonSchema::primitive(PrimitiveType::String));
        
        let schema = JsonSchema::struct_schema(fields);
        let projected_columns = vec![0u64, 2u64]; // Project "name" and "email"
        
        // Create test JSON data
        let json_data = r#"{"name": "John", "age": 30, "email": "<EMAIL>"}"#;
        let reader = Cursor::new(json_data.as_bytes());
        
        // Create processor
        let processor = ProjectionAwareProcessor::new(reader, &schema, &projected_columns, 1000);
        assert!(processor.is_ok());
        
        let processor = processor.unwrap();
        let stats = processor.get_projection_stats();
        assert_eq!(stats.projected_columns, 2);
        assert_eq!(stats.total_columns, 2); // Only projected columns are counted in total
    }

    #[test]
    fn test_projection_utils() {
        let projection_tree = utils::create_test_projection_tree().unwrap();
        assert_eq!(projection_tree.total_columns(), 2); // Only projected columns
        
        let analysis = utils::analyze_projection_effectiveness(10, 3, 1000);
        assert_eq!(analysis.projected_fields, 3);
        assert_eq!(analysis.total_fields, 10);
        assert_eq!(analysis.efficiency_rating, utils::EfficiencyRating::Good); // 30% projection ratio = Good
        assert_eq!(analysis.estimated_savings, 700); // 70% savings
    }

    #[test]
    fn test_path_parsing_logic() {
        let projection_tree = utils::create_test_projection_tree().unwrap();
        
        // Test path that should be parsed
        let name_path = vec![PathToken::Key("name".to_string())];
        assert!(projection_tree.should_parse_path(&name_path));
        
        // Test path that should be skipped (age is not projected)
        let age_path = vec![PathToken::Key("age".to_string())];
        assert!(!projection_tree.should_parse_path(&age_path));
    }
}
