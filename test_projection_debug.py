#!/usr/bin/env python3

import duckdb
import os

def test_projection_pushdown():
    """Test that projection pushdown is working with debug logging"""
    
    # Connect to DuckDB with unsigned extensions enabled
    conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})

    # Load the extension
    extension_path = "./build/debug/json_stream.duckdb_extension"
    if os.path.exists(extension_path):
        conn.execute(f"LOAD '{extension_path}'")
        print("Extension loaded successfully")
    else:
        print(f"Extension not found at {extension_path}")
        return
    
    # Test 1: Full projection (should process all fields)
    print("\n=== TEST 1: Full projection (all fields) ===")
    try:
        result = conn.execute("SELECT * FROM json_stream('test_projection.json')").fetchall()
        print(f"Full projection result: {result}")
    except Exception as e:
        print(f"Full projection error: {e}")

    # Test 2: Partial projection (should skip some fields)
    print("\n=== TEST 2: Partial projection (only id and name) ===")
    try:
        result = conn.execute("SELECT id, name FROM json_stream('test_projection.json')").fetchall()
        print(f"Partial projection result: {result}")
    except Exception as e:
        print(f"Partial projection error: {e}")

    # Test 3: Single field projection (should skip most fields)
    print("\n=== TEST 3: Single field projection (only name) ===")
    try:
        result = conn.execute("SELECT name FROM json_stream('test_projection.json')").fetchall()
        print(f"Single field projection result: {result}")
    except Exception as e:
        print(f"Single field projection error: {e}")
    
    conn.close()

if __name__ == "__main__":
    test_projection_pushdown()
