#!/usr/bin/env python3
"""
Run true deep projection tests to demonstrate memory efficiency when selecting
deeply nested values from complex JSON structures.
"""

import subprocess
import sys
import os
from pathlib import Path
import time


def run_deep_projection_tests():
    """Run true deep projection memory tests."""
    print("🧪 Running True Deep Projection Memory Tests")
    print("=" * 60)
    print("📊 Test scenarios:")
    print("   - Level 3: company.departments[1].teams[1].name")
    print("   - Level 4: company.departments[1].teams[1].projects[1].metrics.large_data")
    print("   - Level 5: company.departments[1].teams[1].projects[1].metrics.analytics.logs[1]")
    print("   - Wide Deep: unused_field_5.nested.deep_field")
    print("   - Wide Metadata: unused_field_20.nested.metadata.tags[1]")
    print()
    
    # Check if extension exists
    if not Path("./build/release/json_stream.duckdb_extension").exists():
        print("❌ Extension not found. Run 'make release' first.")
        return False
    
    # Create memray output directory
    memray_dir = Path("memray_profiles_deep")
    memray_dir.mkdir(exist_ok=True)
    
    # Run only the true deep projection tests
    test_patterns = [
        "test_duckdb_builtin_true_deep_projection_level3",
        "test_extension_true_deep_projection_level3",
        "test_duckdb_builtin_true_deep_projection_level4", 
        "test_extension_true_deep_projection_level4",
        "test_duckdb_builtin_true_deep_projection_level5",
        "test_extension_true_deep_projection_level5",
        "test_duckdb_builtin_wide_deep_projection_unused_fields",
        "test_extension_wide_deep_projection_unused_fields"
    ]
    
    cmd = [
        sys.executable, "-m", "pytest", 
        "-v", "-s",
        "--memray",
        f"--memray-bin-path={memray_dir}",
        "--memray-bin-prefix=deep_proj_",
    ]
    
    # Add specific test patterns
    for pattern in test_patterns:
        cmd.append(f"tests/test_memory_comparison.py::TestMemoryComparison::{pattern}")
    
    print(f"Running deep projection tests...")
    print("⚠️  This tests true projection pushdown into deeply nested JSON structures")
    print()
    
    start_time = time.time()
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n✅ Deep projection tests completed successfully!")
        print(f"⏱️  Total execution time: {total_time:.1f} seconds")
        
        # List generated memray files
        memray_files = list(memray_dir.glob("*.bin"))
        if memray_files:
            print(f"\n📁 Generated {len(memray_files)} memray profile(s):")
            for f in sorted(memray_files):
                print(f"   {f}")
            
            print(f"\n📊 To analyze memory usage, run:")
            print(f"   memray summary {memray_files[0]}")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Deep projection tests failed with return code {e.returncode}")
        return False
    except KeyboardInterrupt:
        print(f"\n⚠️  Tests interrupted by user")
        return False
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False


def main():
    """Main function to run deep projection tests."""
    print("🚀 JSON Streaming Extension - True Deep Projection Test Suite")
    print("=" * 70)
    print()
    print("🎯 Purpose: Test memory efficiency when selecting deeply nested values")
    print("   This demonstrates the real power of projection pushdown in streaming JSON")
    print()
    
    # Ask for confirmation
    response = input("⚠️  Run true deep projection tests? (y/N): ")
    if response.lower() not in ['y', 'yes']:
        print("❌ Tests cancelled by user")
        return False
    
    print("\n🏁 Starting true deep projection tests...")
    success = run_deep_projection_tests()
    
    # Summary
    print(f"\n🏁 Deep Projection Test Suite Summary")
    print("=" * 45)
    
    if success:
        print("🎉 True deep projection tests completed successfully!")
        print("\n📋 Expected Results:")
        print("   - DuckDB built-in: Still loads entire JSON structure")
        print("   - Our extension: Should only parse projected paths")
        print("   - Memory savings: Potentially even higher than basic projection")
        print("\n💡 Key Insight:")
        print("   True projection pushdown should show the biggest memory")
        print("   differences when selecting tiny portions of large JSON files")
        return True
    else:
        print("❌ Deep projection tests failed. Check output above for details.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
