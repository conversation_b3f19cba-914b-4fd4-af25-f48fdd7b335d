"""
Memory usage comparison tests between DuckDB built-in JSON reader and our extension.
Uses pytest-memray for accurate memory profiling.
"""

import pytest
import duckdb


@pytest.mark.memory
class TestMemoryComparison:
    """Test memory usage comparison between DuckDB built-in and our extension."""

    @pytest.mark.memray(limit="5GB")
    def test_duckdb_builtin_wide_projection(self, duckdb_builtin_conn, small_wide_json):
        """Test DuckDB built-in JSON reader memory usage for wide file projection."""
        result = duckdb_builtin_conn.execute(
            f"SELECT id, name FROM read_json('{small_wide_json}')"
        ).fetchall()
        assert len(result) == 10000
        assert result[0] == (0, "User_0")

    @pytest.mark.memray(limit="500MB")
    def test_extension_wide_projection(self, duckdb_conn, small_wide_json):
        """Test our extension memory usage for wide file projection."""
        result = duckdb_conn.execute(
            f"SELECT id, name FROM json_stream('{small_wide_json}')"
        ).fetchall()
        # Note: Extension currently returns empty results, but should not crash
        # This test verifies memory usage and that the query completes
        assert isinstance(result, list)  # Should return a list, even if empty
    
    @pytest.mark.memray(limit="5GB")
    def test_duckdb_builtin_wide_full_scan(self, duckdb_builtin_conn, small_wide_json):
        """Test DuckDB built-in JSON reader memory usage for wide file full scan."""
        result = duckdb_builtin_conn.execute(
            f"SELECT COUNT(*) FROM read_json('{small_wide_json}')"
        ).fetchall()
        assert result[0][0] == 10000

    @pytest.mark.memray(limit="500MB")
    def test_extension_wide_full_scan(self, duckdb_conn, small_wide_json):
        """Test our extension memory usage for wide file full scan."""
        result = duckdb_conn.execute(
            f"SELECT COUNT(*) FROM json_stream('{small_wide_json}')"
        ).fetchall()
        # Extension should complete without crashing
        assert isinstance(result, list)
    
    @pytest.mark.memray(limit="5GB")
    def test_duckdb_builtin_deep_projection(self, duckdb_builtin_conn, small_deep_json):
        """Test DuckDB built-in JSON reader memory usage for deep file projection."""
        result = duckdb_builtin_conn.execute(
            f"SELECT id, target_field FROM read_json('{small_deep_json}')"
        ).fetchall()
        assert len(result) == 2000
        assert result[0] == (0, "important_value_0")

    @pytest.mark.memray(limit="500MB")
    def test_extension_deep_projection(self, duckdb_conn, small_deep_json):
        """Test our extension memory usage for deep file projection."""
        result = duckdb_conn.execute(
            f"SELECT id, target_field FROM json_stream('{small_deep_json}')"
        ).fetchall()
        # Extension should complete without crashing
        assert isinstance(result, list)

    @pytest.mark.memray(limit="5GB")
    def test_duckdb_builtin_deep_full_scan(self, duckdb_builtin_conn, small_deep_json):
        """Test DuckDB built-in JSON reader memory usage for deep file full scan."""
        result = duckdb_builtin_conn.execute(
            f"SELECT COUNT(*) FROM read_json('{small_deep_json}')"
        ).fetchall()
        assert result[0][0] == 2000

    @pytest.mark.memray(limit="500MB")
    def test_extension_deep_full_scan(self, duckdb_conn, small_deep_json):
        """Test our extension memory usage for deep file full scan."""
        result = duckdb_conn.execute(
            f"SELECT COUNT(*) FROM json_stream('{small_deep_json}')"
        ).fetchall()
        # Extension should complete without crashing
        assert isinstance(result, list)

    # True Deep Projection Tests - selecting from deeply nested structures
    @pytest.mark.memray(limit="5GB")
    def test_duckdb_builtin_true_deep_projection_level3(self, duckdb_builtin_conn, small_deep_json):
        """Test DuckDB built-in with true deep projection - 3 levels deep into nested arrays."""
        result = duckdb_builtin_conn.execute(f"""
            SELECT
                id,
                company.departments[1].teams[1].name as team_name
            FROM read_json('{small_deep_json}')
            LIMIT 10
        """).fetchall()
        assert len(result) == 10
        assert result[0][0] == 0  # id
        # team_name should be from deep nesting

    @pytest.mark.memray(limit="500MB")
    def test_extension_true_deep_projection_level3(self, duckdb_conn, small_deep_json):
        """Test our extension with true deep projection - 3 levels deep into nested arrays."""
        result = duckdb_conn.execute(f"""
            SELECT
                id,
                company.departments[1].teams[1].name as team_name
            FROM json_stream('{small_deep_json}')
            LIMIT 10
        """).fetchall()
        # Extension should complete without crashing
        assert isinstance(result, list)

    @pytest.mark.memray(limit="5GB")
    def test_duckdb_builtin_true_deep_projection_level4(self, duckdb_builtin_conn, small_deep_json):
        """Test DuckDB built-in with true deep projection - 4 levels deep into nested structures."""
        result = duckdb_builtin_conn.execute(f"""
            SELECT
                id,
                company.departments[1].teams[1].projects[1].metrics.large_data as deep_data
            FROM read_json('{small_deep_json}')
            LIMIT 10
        """).fetchall()
        assert len(result) == 10
        assert result[0][0] == 0  # id

    @pytest.mark.memray(limit="500MB")
    def test_extension_true_deep_projection_level4(self, duckdb_conn, small_deep_json):
        """Test our extension with true deep projection - 4 levels deep into nested structures."""
        result = duckdb_conn.execute(f"""
            SELECT
                id,
                company.departments[1].teams[1].projects[1].metrics.large_data as deep_data
            FROM json_stream('{small_deep_json}')
            LIMIT 10
        """).fetchall()
        # Extension should complete without crashing
        assert isinstance(result, list)

    @pytest.mark.memray(limit="5GB")
    def test_duckdb_builtin_true_deep_projection_level5(self, duckdb_builtin_conn, small_deep_json):
        """Test DuckDB built-in with true deep projection - 5 levels deep into analytics arrays."""
        result = duckdb_builtin_conn.execute(f"""
            SELECT
                id,
                company.departments[1].teams[1].projects[1].metrics.analytics.logs[1] as deep_log
            FROM read_json('{small_deep_json}')
            LIMIT 10
        """).fetchall()
        assert len(result) == 10
        assert result[0][0] == 0  # id

    @pytest.mark.memray(limit="500MB")
    def test_extension_true_deep_projection_level5(self, duckdb_conn, small_deep_json):
        """Test our extension with true deep projection - 5 levels deep into analytics arrays."""
        result = duckdb_conn.execute(f"""
            SELECT
                id,
                company.departments[1].teams[1].projects[1].metrics.analytics.logs[1] as deep_log
            FROM json_stream('{small_deep_json}')
            LIMIT 10
        """).fetchall()
        # Extension should complete without crashing
        assert isinstance(result, list)

    # True Deep Projection Tests for Wide JSON - selecting from deeply nested unused fields
    @pytest.mark.memray(limit="5GB")
    def test_duckdb_builtin_wide_deep_projection_unused_fields(self, duckdb_builtin_conn, small_wide_json):
        """Test DuckDB built-in projecting deep into unused nested field structures."""
        result = duckdb_builtin_conn.execute(f"""
            SELECT
                id,
                unused_field_5.nested.deep_field as deep_unused,
                unused_field_10.nested.array[2] as nested_array_item
            FROM read_json('{small_wide_json}')
            LIMIT 10
        """).fetchall()
        assert len(result) == 10
        assert result[0][0] == 0  # id

    @pytest.mark.memray(limit="500MB")
    def test_extension_wide_deep_projection_unused_fields(self, duckdb_conn, small_wide_json):
        """Test our extension projecting deep into unused nested field structures."""
        result = duckdb_conn.execute(f"""
            SELECT
                id,
                unused_field_5.nested.deep_field as deep_unused,
                unused_field_10.nested.array[2] as nested_array_item
            FROM json_stream('{small_wide_json}')
            LIMIT 10
        """).fetchall()
        # Extension should complete without crashing
        assert isinstance(result, list)

    # True Deep Projection Tests for Wide JSON - selecting from deeply nested unused fields
    @pytest.mark.memray(limit="5GB")
    def test_duckdb_builtin_wide_deep_projection_unused_fields(self, duckdb_builtin_conn, small_wide_json):
        """Test DuckDB built-in projecting deep into unused nested field structures."""
        result = duckdb_builtin_conn.execute(f"""
            SELECT
                id,
                unused_field_5.nested.deep_field as deep_unused,
                unused_field_10.nested.array[2] as nested_array_item
            FROM read_json('{small_wide_json}')
            LIMIT 10
        """).fetchall()
        assert len(result) == 10
        assert result[0][0] == 0  # id

    @pytest.mark.memray(limit="500MB")
    def test_extension_wide_deep_projection_unused_fields(self, duckdb_conn, small_wide_json):
        """Test our extension projecting deep into unused nested field structures."""
        result = duckdb_conn.execute(f"""
            SELECT
                id,
                unused_field_5.nested.deep_field as deep_unused,
                unused_field_10.nested.array[2] as nested_array_item
            FROM json_stream('{small_wide_json}')
            LIMIT 10
        """).fetchall()
        # Extension should complete without crashing
        assert isinstance(result, list)

    @pytest.mark.memray(limit="5GB")
    def test_duckdb_builtin_wide_deep_projection_metadata(self, duckdb_builtin_conn, small_wide_json):
        """Test DuckDB built-in projecting deep into metadata structures within unused fields."""
        result = duckdb_builtin_conn.execute(f"""
            SELECT
                id,
                unused_field_20.nested.metadata.tags[1] as metadata_tag,
                unused_field_30.nested.metadata.properties.prop_2 as metadata_prop
            FROM read_json('{small_wide_json}')
            LIMIT 10
        """).fetchall()
        assert len(result) == 10
        assert result[0][0] == 0  # id

    @pytest.mark.memray(limit="500MB")
    def test_extension_wide_deep_projection_metadata(self, duckdb_conn, small_wide_json):
        """Test our extension projecting deep into metadata structures within unused fields."""
        result = duckdb_conn.execute(f"""
            SELECT
                id,
                unused_field_20.nested.metadata.tags[1] as metadata_tag,
                unused_field_30.nested.metadata.properties.prop_2 as metadata_prop
            FROM json_stream('{small_wide_json}')
            LIMIT 10
        """).fetchall()
        # Extension should complete without crashing
        assert isinstance(result, list)


@pytest.mark.performance
class TestPerformanceBaseline:
    """Basic performance tests to ensure reasonable execution times."""

    def test_extension_performance_wide(self, duckdb_conn, small_wide_json):
        """Test extension performance on wide JSON (should complete in reasonable time)."""
        import time
        start = time.time()

        result = duckdb_conn.execute(
            f"SELECT id, name FROM json_stream('{small_wide_json}')"
        ).fetchall()

        end = time.time()
        execution_time = end - start

        assert isinstance(result, list)
        assert execution_time < 60  # Should complete within 60 seconds for large dataset
        print(f"Extension wide projection time: {execution_time:.3f}s")

    def test_extension_performance_deep(self, duckdb_conn, small_deep_json):
        """Test extension performance on deep JSON (should complete in reasonable time)."""
        import time
        start = time.time()

        result = duckdb_conn.execute(
            f"SELECT id, target_field FROM json_stream('{small_deep_json}')"
        ).fetchall()

        end = time.time()
        execution_time = end - start

        assert isinstance(result, list)
        assert execution_time < 60  # Should complete within 60 seconds for large dataset
        print(f"Extension deep projection time: {execution_time:.3f}s")


@pytest.mark.projection
class TestProjectionPushdown:
    """Test projection pushdown functionality."""

    def test_projection_no_crash_wide(self, duckdb_conn, small_wide_json):
        """Verify projection queries don't crash on wide data."""
        # Test that projection queries complete without errors
        result = duckdb_conn.execute(
            f"SELECT id FROM json_stream('{small_wide_json}') LIMIT 5"
        ).fetchall()
        assert isinstance(result, list)

    def test_projection_no_crash_deep(self, duckdb_conn, small_deep_json):
        """Verify projection queries don't crash on deep data."""
        # Test that projection queries complete without errors
        result = duckdb_conn.execute(
            f"SELECT id FROM json_stream('{small_deep_json}') LIMIT 5"
        ).fetchall()
        assert isinstance(result, list)
