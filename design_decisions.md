# Design Decisions for DuckDB JSON Stream Extension

This document captures the key architectural and design decisions made during the development of the JSON streaming extension.

## Core Architecture Decisions

### 1. Streaming vs. Batch Processing

**Decision**: Use streaming JSON parsing with configurable chunk sizes
**Rationale**:
- Enables processing of files larger than available memory
- Provides predictable memory usage patterns
- Allows for better resource management in multi-tenant environments
- Maintains compatibility with DuckDB's chunked processing model

**Alternatives Considered**:
- Full file loading: Rejected due to memory constraints
- Fixed small chunks: Rejected due to performance overhead
- Adaptive chunking: Deferred for future optimization

### 2. Projection Pushdown Strategy

**Decision**: Build projection tree from schema and projected columns, skip parsing of non-projected fields
**Rationale**:
- Significant performance improvement for selective queries
- Reduces both CPU and memory usage
- Aligns with DuckDB's columnar processing philosophy
- Enables efficient processing of wide JSON documents

**Implementation Details**:
- Projection tree built during bind phase
- Path-based field skipping during parsing
- Recursive projection support for nested structures

### 3. Type System Design

**Decision**: Native DuckDB types (STRUCT/LIST) with comprehensive schema inference
**Rationale**:
- Eliminates VARCHAR fallbacks that lose type information
- Provides better query performance through proper typing
- Enables DuckDB's native optimizations for structured data
- Maintains data fidelity for complex nested structures

**Schema Inference Strategy**:
- Sample-based inference for performance
- Conservative type merging to handle variations
- Configurable inference parameters

### 4. Memory Management

**Decision**: Stack-based parsing with explicit memory tracking
**Rationale**:
- Eliminates recursion depth limits
- Provides predictable memory usage
- Enables better error handling and recovery
- Supports arbitrarily deep nesting

**Memory Optimization Techniques**:
- Reusable vector allocations
- Chunked processing with configurable sizes
- Lazy allocation for unused projection paths

### 5. Error Handling Strategy

**Decision**: Comprehensive error types with configurable handling modes
**Rationale**:
- Production environments need flexible error handling
- Different use cases require different error tolerance
- Detailed error context aids in debugging
- Supports both strict validation and lenient processing

**Error Modes**:
- Strict: Fail fast on any error
- Lenient: Log errors and continue with defaults
- Collect: Accumulate errors for batch reporting

## Architecture Components

### 1. Schema System (`JsonSchema`)
Recursive schema representation that handles arbitrary nesting:

```rust
#[derive(Debug, Clone)]
pub enum JsonSchema {
    Primitive(PrimitiveType),
    Struct {
        fields: IndexMap<String, JsonSchema>,
        field_order: Vec<String>,
    },
    List {
        element_schema: Box<JsonSchema>,
        max_capacity: usize,
    },
}

#[derive(Debug, Clone)]
pub enum PrimitiveType {
    Boolean,
    Integer,
    Float,
    String,
    Null,
}
```

**Key Features:**
- Preserves field ordering from first observation
- Calculates memory requirements for all nested levels
- Supports projection pushdown through nested paths
- Handles schema evolution and missing fields

### 2. Streaming Parser Integration (`JsonStreamProcessor`)
Integrates struson with DuckDB's vector API:

```rust
pub struct JsonStreamProcessor {
    reader: JsonStreamReader<BufReader<File>>,
    schema: JsonSchema,
    projection: Option<Vec<String>>,
    current_path: Vec<String>,
}
```

**Responsibilities:**
- Stream JSON using struson without full materialization
- Apply projection pushdown to skip unneeded fields
- Track current position in nested structure
- Handle different JSON root types (object, array, primitive)

### 3. Vector Writing System (`VectorPopulator`)
Converts JSON data to native DuckDB vectors:

```rust
pub struct VectorPopulator {
    schema: JsonSchema,
    row_count: usize,
    current_row: usize,
}

impl VectorPopulator {
    fn populate_struct_vector(&mut self, vector: &mut StructVector, data: JsonValue);
    fn populate_list_vector(&mut self, vector: &mut ListVector, data: JsonValue);
    fn populate_primitive_vector<T>(&mut self, vector: &mut FlatVector, data: JsonValue);
}
```

**Key Features:**
- Recursive vector population for arbitrary nesting
- Proper offset management for ListVector
- Memory-efficient child vector handling
- Stack-based processing to avoid recursion limits

### 4. JSON Root Type Handler (`JsonRootProcessor`)
Handles different JSON document structures:

```rust
pub enum JsonRootType {
    SingleObject,           // {"name": "Alice"}
    ArrayOfObjects,         // [{"name": "Alice"}, {"name": "Bob"}]
    ArrayOfPrimitives,      // [1, 2, 3]
    SinglePrimitive,        // 42
    MixedArray,            // [1, {"name": "Alice"}, [1,2,3]]
}
```

**Processing Logic:**
- Single objects → Single row with STRUCT columns
- Array of objects → Multiple rows, each object becomes a row
- Array of primitives → Multiple rows, each primitive becomes a row
- Mixed arrays → Multiple rows with UNION type handling

## Memory Management Strategy

### Stack-Based Processing
Instead of recursion, use explicit stack to handle nesting:

```rust
struct ProcessingFrame {
    json_path: Vec<String>,
    vector_type: VectorType,
    current_index: usize,
    remaining_elements: usize,
}

struct ProcessingStack {
    frames: Vec<ProcessingFrame>,
    max_depth: usize,
}
```

### Streaming Buffer Management
- Read JSON in chunks, not entire file
- Process rows in batches (DuckDB's vector size)
- Release memory as soon as data is written to vectors
- Use struson's seeking capabilities for projection pushdown

## Schema Inference Process

### Two-Phase Approach
1. **Schema Discovery Phase**: 
   - Sample first N objects (configurable)
   - Build comprehensive schema with all possible fields
   - Calculate memory requirements and capacities
   
2. **Data Processing Phase**:
   - Use discovered schema to create DuckDB vectors
   - Stream through entire file populating vectors
   - Handle missing fields as NULL values

### Schema Merging for Multiple Files
When reading multiple JSON files:
- Merge schemas using union of all fields
- Handle type conflicts (promote to most general type)
- Maintain field ordering from first file

## Extension Integration

### Table Function Implementation
```rust
struct JsonStreamVTab;

impl VTab for JsonStreamVTab {
    type BindData = JsonStreamBindData;
    type InitData = JsonStreamInitData;
    
    fn bind(bind: &BindInfo) -> Result<Self::BindData>;
    fn init(init: &InitInfo) -> Result<Self::InitData>;
    fn func(func: &TableFunctionInfo<Self>, output: &mut DataChunkHandle) -> Result<()>;
}
```

### Bind Phase
- Parse file path and options
- Perform schema inference
- Create DuckDB column definitions
- Store schema and configuration in bind data

### Execution Phase  
- Initialize streaming parser
- Process JSON in chunks
- Populate DuckDB vectors directly
- Handle backpressure and memory limits

## Error Handling Strategy
- Malformed JSON → Return descriptive error with location
- Type mismatches → Attempt coercion, fallback to NULL
- Memory limits → Graceful degradation with smaller chunks
- File access errors → Standard DuckDB error reporting

## Testing Strategy
- Unit tests for each component
- Integration tests with various JSON shapes
- Memory usage tests with large files
- Compatibility tests against DuckDB's native JSON reader
- Performance benchmarks vs existing implementations

## Future Optimizations
- Parallel processing for multiple files
- Adaptive schema inference based on file size
- Custom memory allocators for vector data
- SIMD optimizations for primitive type conversion
