# DuckDB JSON Stream Extension

A high-performance, memory-efficient streaming JSON reader extension for DuckDB with advanced projection pushdown capabilities.

## Overview

This extension provides a streaming JSON reader that processes JSON data with minimal memory usage through:

- **Projection Pushdown**: Only parses and processes projected columns, skipping unnecessary data
- **Streaming Architecture**: Processes JSON incrementally without loading entire files into memory
- **Native DuckDB Types**: Direct population of STRUCT and LIST vectors without VARCHAR fallbacks
- **Nested Data Support**: Handles arbitrarily nested JSON structures with proper type preservation
- **Memory Optimization**: Stack-based parsing with configurable chunk sizes for optimal memory usage

## Key Features

### Projection Pushdown
- Analyzes SQL queries to determine which JSON fields are actually needed
- Skips parsing of non-projected fields entirely, reducing CPU and memory usage
- Supports deep nested projections (e.g., `SELECT user.profile.name FROM json_table`)

### Streaming Processing
- Uses struson crate for efficient streaming JSON parsing
- Processes data in configurable chunks to control memory usage
- No arbitrary depth limits - handles deeply nested structures

### Type System
- Comprehensive schema inference from JSON data
- Native DuckDB STRUCT and LIST type support
- Proper null handling with configurable strategies
- Type validation and error reporting

### Error Handling
- Comprehensive error types for different failure modes
- Configurable error handling strategies (strict, lenient, collect)
- Detailed error context with path information

## Dependencies

- Rust toolchain (1.70+)
- DuckDB development headers
- Python 3.8+ with uv package manager
- [Make](https://www.gnu.org/software/make)

Installing these dependencies will vary per platform:
- For Linux, these come generally pre-installed or are available through the distro-specific package manager.
- For MacOS, [homebrew](https://formulae.brew.sh/).
- For Windows, [chocolatey](https://community.chocolatey.org/).

## Building
After installing the dependencies, building is a two-step process. Firstly run:
```shell
make configure
```
This will ensure a Python venv is set up with DuckDB and DuckDB's test runner installed.

Then, to build the extension run:
```shell
make debug
```
This delegates the build process to cargo, which will produce a shared library in `target/debug/`.

To create optimized release binaries, simply run `make release` instead.

### Build Commands

```bash
# Debug build
make debug

# Release build
make release

# Run unit tests
cargo test --lib

# Run integration tests
uv run pytest
```

## Testing

The extension includes comprehensive test coverage:

```bash
# Run all Rust unit tests (38 tests)
cargo test --lib

# Run specific test modules
cargo test projection::tests
cargo test integration_tests::projection_tests
cargo test null_handling::tests
cargo test error_handling::tests

# Run Python integration tests
uv run pytest tests/

# Memory profiling tests
uv run pytest tests/ --memray
```

### Test Categories

1. **Unit Tests**: Test individual components in isolation
2. **Integration Tests**: Test component interactions and end-to-end scenarios
3. **Memory Tests**: Verify memory efficiency and projection effectiveness
4. **Performance Tests**: Benchmark parsing and projection performance

## Loading the Extension

After building, you can load the extension in DuckDB:

```sql
LOAD './build/debug/json_stream.duckdb_extension';
```

## Usage

### Basic JSON Reading

```sql
-- Read JSON file with all columns
SELECT * FROM read_json_stream('data.json');

-- Project specific columns (uses projection pushdown)
SELECT user.name, user.email FROM read_json_stream('users.json');
```

### Nested Data Access

```sql
-- Access deeply nested fields
SELECT
    company.name,
    company.departments[1].employees[1].name,
    company.departments[1].employees[1].skills
FROM read_json_stream('company.json');
```

### Configuration Options

```sql
-- Configure chunk size for memory optimization
SELECT * FROM read_json_stream('large_file.json', chunk_size=1000);

-- Configure error handling
SELECT * FROM read_json_stream('data.json', error_mode='lenient');
```

### Known issues
This is a bit of a footgun, but the extensions produced by this template may (or may not) be broken on windows on python3.11
with the following error on extension load:
```shell
IO Error: Extension '<name>.duckdb_extension' could not be loaded: The specified module could not be found
```
This was resolved by using python 3.12

## Architecture

### Core Components

1. **Schema Module** (`src/schema.rs`): JSON schema inference and type management
2. **Projection Module** (`src/projection.rs`): Projection tree construction and path analysis
3. **Stack Parser** (`src/stack_parser.rs`): Memory-efficient streaming JSON parser
4. **Vector Populator** (`src/vector_populator.rs`): DuckDB vector population logic
5. **Nested Vectors** (`src/nested_vectors.rs`): Specialized handling for LIST and STRUCT types
6. **Null Handling** (`src/null_handling.rs`): Missing field and null value management
7. **Error Handling** (`src/error_handling.rs`): Comprehensive error reporting system

### Data Flow

1. **Schema Inference**: Analyze JSON structure to determine types and nesting
2. **Projection Analysis**: Build projection tree from SQL query column requirements
3. **Streaming Parse**: Parse JSON incrementally, skipping non-projected fields
4. **Vector Population**: Directly populate DuckDB vectors with parsed data
5. **Chunk Processing**: Process data in configurable chunks for memory efficiency

## Performance Characteristics

### Memory Usage
- O(chunk_size) memory usage regardless of file size
- Projection pushdown reduces memory by skipping unused fields
- Stack-based parsing eliminates recursion overhead

### CPU Efficiency
- Only parses projected JSON fields
- Direct vector population without intermediate representations
- Optimized path checking for nested projections

### Scalability
- Handles files larger than available memory
- No arbitrary nesting depth limits
- Configurable chunk sizes for different workloads

## Configuration

### Environment Variables
- `JSON_STREAM_CHUNK_SIZE`: Default chunk size (default: 1000)
- `JSON_STREAM_ERROR_MODE`: Default error handling mode (strict/lenient/collect)
- `JSON_STREAM_MAX_DEPTH`: Maximum nesting depth for safety (default: unlimited)

### Runtime Parameters
- `chunk_size`: Number of rows to process per chunk
- `error_mode`: How to handle parsing errors
- `null_strategy`: How to handle missing fields
- `memory_limit`: Maximum memory usage per chunk

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass: `cargo test --lib`
5. Submit a pull request

### Development Guidelines
- Follow Rust naming conventions
- Add comprehensive tests for new features
- Update documentation for API changes
- Use `cargo fmt` for code formatting
- Run `cargo clippy` for linting

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Built on the DuckDB extension template for Rust
- Uses the struson crate for efficient JSON streaming
- Inspired by DuckDB's Parquet reader architecture
